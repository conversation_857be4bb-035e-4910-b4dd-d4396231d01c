% 完整系统测试脚本
% 测试GP-DDPG导纳控制系统的所有组件

function test_complete_system()
    fprintf('=== 完整GP-DDPG系统测试 ===\n');
    
    % 清理环境
    close all;
    clc;
    
    % 设置测试参数
    test_config = struct();
    test_config.max_episodes = 10;           % 测试用的较少轮数
    test_config.max_steps_per_episode = 200; % 测试用的较少步数
    test_config.verbose = true;
    
    try
        % 测试1: 基础组件测试
        fprintf('\n1. 基础组件测试\n');
        test_basic_components();
        
        % 测试2: 集成测试
        fprintf('\n2. 集成测试\n');
        test_integration(test_config);
        
        % 测试3: 训练测试
        fprintf('\n3. 训练测试\n');
        test_training(test_config);
        
        % 测试4: 性能测试
        fprintf('\n4. 性能测试\n');
        test_performance();
        
        fprintf('\n=== 所有测试通过 ✓ ===\n');
        fprintf('系统已准备好进行完整训练\n');
        
        % 询问是否运行完整训练
        user_input = input('\n是否运行完整训练？(y/n): ', 's');
        if strcmpi(user_input, 'y') || strcmpi(user_input, 'yes')
            run_full_training();
        end
        
    catch ME
        fprintf('\n❌ 测试失败: %s\n', ME.message);
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
        
        % 显示详细错误信息
        if length(ME.stack) > 1
            fprintf('调用堆栈:\n');
            for i = 1:min(3, length(ME.stack))
                fprintf('  %d. %s (第%d行)\n', i, ME.stack(i).name, ME.stack(i).line);
            end
        end
        
        provide_debugging_info(ME);
    end
end

function test_basic_components()
    % 测试基础组件
    
    fprintf('   测试环境初始化...\n');
    env = force_control_environment();
    state = env.reset();
    assert(length(state) == 6, '状态维度错误');
    fprintf('   ✓ 环境初始化成功\n');
    
    fprintf('   测试GP-DDPG控制器初始化...\n');
    controller = gp_ddpg_controller(6, 2);
    assert(~isempty(controller), '控制器初始化失败');
    fprintf('   ✓ GP-DDPG控制器初始化成功\n');
    
    fprintf('   测试策略切换器初始化...\n');
    switcher = policy_switcher([], controller);
    assert(~isempty(switcher), '策略切换器初始化失败');
    fprintf('   ✓ 策略切换器初始化成功\n');
    
    fprintf('   测试基本功能...\n');
    action = controller.get_action(state, true);
    assert(length(action) == 2, '动作维度错误');
    assert(action(1) >= 0 && action(1) <= 300, '阻尼参数超出范围');
    assert(action(2) >= 0 && action(2) <= 10, '刚度参数超出范围');
    fprintf('   ✓ 动作生成正常: B=%.2f, K=%.2f\n', action(1), action(2));
    
    [next_state, reward, done] = env.step(action);
    assert(length(next_state) == 6, '下一状态维度错误');
    assert(isfinite(reward), '奖励值异常');
    fprintf('   ✓ 环境步进正常: 奖励=%.3f\n', reward);
end

function test_integration(config)
    % 测试组件集成
    
    fprintf('   创建集成系统...\n');
    optimizer = admittance_optimizer('max_episodes', config.max_episodes, ...
                                   'max_steps_per_episode', config.max_steps_per_episode);
    
    fprintf('   测试单轮训练...\n');
    [episode_reward, episode_force_error, convergence_time] = optimizer.run_episode();
    
    assert(isfinite(episode_reward), '轮次奖励异常');
    assert(isfinite(episode_force_error), '力误差异常');
    fprintf('   ✓ 单轮训练完成: 奖励=%.3f, 力误差=%.3f\n', episode_reward, episode_force_error);
    
    % 测试经验存储
    buffer_info = optimizer.gp_ddpg_controller.experience_buffer;
    assert(buffer_info.size > 0, '经验缓冲区为空');
    fprintf('   ✓ 经验存储正常: %d条经验\n', buffer_info.size);
    
    % 测试GP数据更新
    optimizer.gp_ddpg_controller.update_gp_data();
    gp_data_size = size(optimizer.gp_ddpg_controller.gp_data_X, 1);
    fprintf('   ✓ GP数据更新完成: %d个精英样本\n', gp_data_size);
end

function test_training(config)
    % 测试训练过程
    
    fprintf('   开始短期训练测试...\n');
    optimizer = admittance_optimizer('max_episodes', config.max_episodes, ...
                                   'max_steps_per_episode', config.max_steps_per_episode);
    
    % 记录初始性能
    initial_performance = optimizer.test_performance('sinusoidal');
    fprintf('   初始性能: 力误差=%.3f\n', initial_performance.final_force_error);
    
    % 运行训练
    optimizer.train();
    
    % 检查训练结果
    assert(length(optimizer.episode_rewards) == config.max_episodes, '训练轮数不匹配');
    assert(~isempty(optimizer.episode_force_errors), '力误差记录为空');
    
    % 记录最终性能
    final_performance = optimizer.test_performance('sinusoidal');
    fprintf('   最终性能: 力误差=%.3f\n', final_performance.final_force_error);
    
    % 检查是否有改善（允许一定的随机性）
    improvement = initial_performance.final_force_error - final_performance.final_force_error;
    fprintf('   ✓ 训练完成，性能变化: %.3f\n', improvement);
    
    % 打印训练统计
    optimizer.gp_ddpg_controller.print_status();
    optimizer.policy_switcher.print_status();
end

function test_performance()
    % 测试性能评估功能
    
    fprintf('   测试性能评估...\n');
    
    % 创建优化器并进行少量训练
    optimizer = admittance_optimizer('max_episodes', 5, 'max_steps_per_episode', 100);
    optimizer.train();
    
    % 测试不同场景
    scenarios = {'sinusoidal', 'step'};
    
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        fprintf('   测试场景: %s\n', scenario);
        
        results = optimizer.test_performance(scenario);
        
        assert(isfinite(results.final_force_error), '最终力误差异常');
        assert(isfinite(results.max_force_error), '最大力误差异常');
        assert(results.settling_time >= 0, '调节时间异常');
        
        fprintf('     ✓ 场景测试完成: 最终误差=%.3f, 最大误差=%.3f, 调节时间=%.3f\n', ...
                results.final_force_error, results.max_force_error, results.settling_time);
    end
    
    % 测试可视化
    try
        optimizer.plot_training_results();
        fprintf('   ✓ 可视化测试成功\n');
        close all; % 关闭测试图形
    catch
        fprintf('   ⚠ 可视化测试跳过（可能需要图形界面）\n');
    end
end

function run_full_training()
    % 运行完整训练
    
    fprintf('\n=== 开始完整训练 ===\n');
    
    try
        % 使用默认参数运行完整训练
        run_ddpg_admittance_control();
        
    catch ME
        fprintf('完整训练失败: %s\n', ME.message);
        fprintf('建议先运行测试模式确保系统正常\n');
    end
end

function provide_debugging_info(ME)
    % 提供调试信息
    
    fprintf('\n=== 调试信息 ===\n');
    
    if contains(ME.message, 'Matrix dimensions')
        fprintf('• 矩阵维度问题：检查状态和动作向量的维度匹配\n');
        fprintf('  状态应为6维，动作应为2维\n');
    elseif contains(ME.message, 'Index exceeds')
        fprintf('• 索引超出范围：检查数组访问和缓冲区大小\n');
    elseif contains(ME.message, 'Out of memory')
        fprintf('• 内存不足：减少缓冲区大小或训练参数\n');
        fprintf('  例如：buffer_size=1000, max_episodes=10\n');
    elseif contains(ME.message, 'Undefined')
        fprintf('• 未定义变量或函数：检查文件路径和函数名\n');
    elseif contains(ME.message, 'predict') || contains(ME.message, 'trainNetwork')
        fprintf('• 深度学习工具箱问题：系统会自动使用简化网络实现\n');
    else
        fprintf('• 通用调试建议：\n');
        fprintf('  1. 确保所有.m文件在MATLAB路径中\n');
        fprintf('  2. 检查MATLAB版本兼容性（建议R2020b+）\n');
        fprintf('  3. 尝试重启MATLAB清除缓存\n');
        fprintf('  4. 检查系统内存是否充足\n');
    end
    
    fprintf('\n如需详细调试，请设置断点并逐步执行\n');
end

function print_system_info()
    % 打印系统信息
    
    fprintf('\n=== 系统信息 ===\n');
    fprintf('MATLAB版本: %s\n', version);
    
    % 检查工具箱
    toolboxes = {'Deep Learning Toolbox', 'Control System Toolbox', 'Statistics and Machine Learning Toolbox'};
    for i = 1:length(toolboxes)
        if license('test', strrep(toolboxes{i}, ' ', '_'))
            fprintf('✓ %s: 可用\n', toolboxes{i});
        else
            fprintf('✗ %s: 不可用\n', toolboxes{i});
        end
    end
    
    % 内存信息
    try
        [~, sys] = memory;
        fprintf('系统内存: %.1f GB\n', sys.PhysicalMemory.Total / 1024^3);
    catch
        fprintf('无法获取内存信息\n');
    end
    
    fprintf('==================\n');
end

% 主执行部分
function main()
    print_system_info();
    test_complete_system();
end

function test_optimized_charts()
    % 测试优化后的图表效果
    % 生成模拟数据来验证图表的视觉效果
    
    fprintf('=== 测试优化后的图表效果 ===\n');
    
    % 创建模拟的对比结果数据
    comparison_results = create_mock_comparison_data();
    
    % 配置参数
    config = struct();
    config.save_results = true;
    config.plot_results = true;
    
    % 绘制优化后的对比图表
    plot_comparison_results(comparison_results, config);
    
    fprintf('图表测试完成！\n');
end

function comparison_results = create_mock_comparison_data()
    % 创建模拟的对比数据
    
    scenarios = {'sinusoidal', 'step', 'variable_stiffness'};
    comparison_results = struct();
    
    % 为每个场景创建模拟数据
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        
        % 传统方法结果（性能较差）
        traditional = struct();
        traditional.final_force_error = 20 + 5*rand();  % 20-25N
        traditional.settling_time = 12 + 3*rand();      % 12-15s
        traditional.rmse = 8 + 2*rand();                % 8-10N
        traditional.overshoot = 15 + 5*rand();          % 15-20%
        traditional.method_name = '传统阻抗控制';
        
        % 自适应方法结果（中等性能）
        adaptive = struct();
        adaptive.final_force_error = 15 + 3*rand();     % 15-18N
        adaptive.settling_time = 10 + 2*rand();         % 10-12s
        adaptive.rmse = 6 + 1.5*rand();                 % 6-7.5N
        adaptive.overshoot = 10 + 3*rand();             % 10-13%
        adaptive.method_name = '自适应阻抗控制';
        
        % GP-DDPG结果（最佳性能）
        ddpg = struct();
        ddpg.final_force_error = 3 + 2*rand();          % 3-5N
        ddpg.settling_time = 4 + 1*rand();              % 4-5s
        ddpg.rmse = 2 + 0.5*rand();                     % 2-2.5N
        ddpg.overshoot = 2 + 1*rand();                  % 2-3%
        ddpg.method_name = 'GP-DDPG控制';
        
        % 计算性能改善
        improvement = struct();
        
        % 相对于传统方法的改善
        improvement.vs_traditional.force_error = (traditional.final_force_error - ddpg.final_force_error) / traditional.final_force_error * 100;
        improvement.vs_traditional.settling_time = (traditional.settling_time - ddpg.settling_time) / traditional.settling_time * 100;
        improvement.vs_traditional.rmse = (traditional.rmse - ddpg.rmse) / traditional.rmse * 100;
        
        % 相对于自适应方法的改善
        improvement.vs_adaptive.force_error = (adaptive.final_force_error - ddpg.final_force_error) / adaptive.final_force_error * 100;
        improvement.vs_adaptive.settling_time = (adaptive.settling_time - ddpg.settling_time) / adaptive.settling_time * 100;
        improvement.vs_adaptive.rmse = (adaptive.rmse - ddpg.rmse) / adaptive.rmse * 100;
        
        % 存储结果
        comparison_results.(scenario).traditional = traditional;
        comparison_results.(scenario).adaptive = adaptive;
        comparison_results.(scenario).ddpg = ddpg;
        comparison_results.(scenario).improvement = improvement;
        
        % 打印模拟数据
        fprintf('场景 %s:\n', scenario);
        fprintf('  传统方法: 力误差=%.1fN, 调节时间=%.1fs, RMSE=%.1fN\n', ...
                traditional.final_force_error, traditional.settling_time, traditional.rmse);
        fprintf('  自适应方法: 力误差=%.1fN, 调节时间=%.1fs, RMSE=%.1fN\n', ...
                adaptive.final_force_error, adaptive.settling_time, adaptive.rmse);
        fprintf('  GP-DDPG: 力误差=%.1fN, 调节时间=%.1fs, RMSE=%.1fN\n', ...
                ddpg.final_force_error, ddpg.settling_time, ddpg.rmse);
        fprintf('  改善: 力误差%.1f%%, 调节时间%.1f%% (相对传统)\n', ...
                improvement.vs_traditional.force_error, improvement.vs_traditional.settling_time);
    end
    
    % 添加模拟的训练历史数据
    comparison_results.training_history = struct();
    comparison_results.training_history.episode_rewards = generate_training_curve();
end

function rewards = generate_training_curve()
    % 生成模拟的训练收敛曲线
    
    num_episodes = 100;
    rewards = zeros(1, num_episodes);
    
    % 模拟训练过程：初期波动大，后期收敛
    for i = 1:num_episodes
        base_reward = -1000 + 800 * (1 - exp(-i/20));  % 指数收敛
        noise = 100 * exp(-i/15) * randn();            % 噪声逐渐减小
        rewards(i) = base_reward + noise;
    end
    
    % 平滑处理
    if length(rewards) > 5
        rewards = smooth(rewards, 5);
    end
end

function plot_comparison_results(comparison_results, config)
    % 绘制对比结果（使用优化后的代码）
    
    scenarios = {'sinusoidal', 'step', 'variable_stiffness'};
    
    % 创建图形窗口
    figure('Position', [100, 100, 1200, 800]);  % 增加高度以适应2x3布局
    
    % 力误差对比
    subplot(2, 3, 1);
    methods = {'传统阻抗', '自适应阻抗', 'GP-DDPG'};
    force_errors = zeros(length(scenarios), 3);
    
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        force_errors(i, 1) = comparison_results.(scenario).traditional.final_force_error;
        force_errors(i, 2) = comparison_results.(scenario).adaptive.final_force_error;
        force_errors(i, 3) = comparison_results.(scenario).ddpg.final_force_error;
    end
    
    % 创建柱状图并设置颜色
    h1 = bar(force_errors);
    h1(1).FaceColor = [0.3, 0.7, 0.9];  % 浅蓝色 - 传统方法
    h1(2).FaceColor = [0.9, 0.6, 0.3];  % 橙色 - 自适应方法
    h1(3).FaceColor = [0.2, 0.8, 0.2];  % 绿色 - GP-DDPG
    
    % 添加数值标签
    for i = 1:length(scenarios)
        for j = 1:3
            if force_errors(i, j) > 0
                text(i + (j-2)*0.27, force_errors(i, j) + max(force_errors(:))*0.02, ...
                     sprintf('%.1f', force_errors(i, j)), ...
                     'HorizontalAlignment', 'center', 'FontSize', 8);
            end
        end
    end
    
    set(gca, 'XTickLabel', scenarios);
    ylabel('最终力误差 (N)');
    title('力跟踪精度对比');
    legend(methods, 'Location', 'best');
    grid on;
    
    % 调节时间对比
    subplot(2, 3, 2);
    settling_times = zeros(length(scenarios), 3);
    
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        settling_times(i, 1) = comparison_results.(scenario).traditional.settling_time;
        settling_times(i, 2) = comparison_results.(scenario).adaptive.settling_time;
        settling_times(i, 3) = comparison_results.(scenario).ddpg.settling_time;
    end
    
    % 创建柱状图并设置颜色
    h2 = bar(settling_times);
    h2(1).FaceColor = [0.3, 0.7, 0.9];  % 浅蓝色 - 传统方法
    h2(2).FaceColor = [0.9, 0.6, 0.3];  % 橙色 - 自适应方法
    h2(3).FaceColor = [0.2, 0.8, 0.2];  % 绿色 - GP-DDPG
    
    % 添加数值标签
    for i = 1:length(scenarios)
        for j = 1:3
            if settling_times(i, j) > 0
                text(i + (j-2)*0.27, settling_times(i, j) + max(settling_times(:))*0.02, ...
                     sprintf('%.1f', settling_times(i, j)), ...
                     'HorizontalAlignment', 'center', 'FontSize', 8);
            end
        end
    end
    
    set(gca, 'XTickLabel', scenarios);
    ylabel('调节时间 (s)');
    title('收敛速度对比');
    legend(methods, 'Location', 'best');
    grid on;
    
    % 性能改善百分比
    subplot(2, 3, 3);
    improvements_vs_traditional = zeros(length(scenarios), 1);
    improvements_vs_adaptive = zeros(length(scenarios), 1);
    
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        improvements_vs_traditional(i) = comparison_results.(scenario).improvement.vs_traditional.settling_time;
        improvements_vs_adaptive(i) = comparison_results.(scenario).improvement.vs_adaptive.settling_time;
    end
    
    % 确保改善百分比为正值且在合理范围内
    improvements_vs_traditional = max(min(improvements_vs_traditional, 100), 0);
    improvements_vs_adaptive = max(min(improvements_vs_adaptive, 100), 0);
    
    % 创建柱状图
    h3 = bar([improvements_vs_traditional, improvements_vs_adaptive]);
    h3(1).FaceColor = [0.2, 0.6, 0.8];  % 蓝色
    h3(2).FaceColor = [0.8, 0.4, 0.2];  % 橙色
    
    % 添加数值标签
    for i = 1:length(scenarios)
        text(i-0.2, improvements_vs_traditional(i)+2, sprintf('%.1f%%', improvements_vs_traditional(i)), ...
             'HorizontalAlignment', 'center', 'FontSize', 9);
        text(i+0.2, improvements_vs_adaptive(i)+2, sprintf('%.1f%%', improvements_vs_adaptive(i)), ...
             'HorizontalAlignment', 'center', 'FontSize', 9);
    end
    
    set(gca, 'XTickLabel', scenarios);
    ylabel('调节时间改善 (%)');
    title('GP-DDPG性能改善');
    legend({'相对传统方法', '相对自适应方法'}, 'Location', 'best');
    ylim([0, max([improvements_vs_traditional; improvements_vs_adaptive]) * 1.2]);
    grid on;
    
    % RMSE对比
    subplot(2, 3, 4);
    rmse_values = zeros(length(scenarios), 3);
    
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        rmse_values(i, 1) = comparison_results.(scenario).traditional.rmse;
        rmse_values(i, 2) = comparison_results.(scenario).adaptive.rmse;
        rmse_values(i, 3) = comparison_results.(scenario).ddpg.rmse;
    end
    
    % 创建柱状图并设置颜色
    h4 = bar(rmse_values);
    h4(1).FaceColor = [0.3, 0.7, 0.9];  % 浅蓝色 - 传统方法
    h4(2).FaceColor = [0.9, 0.6, 0.3];  % 橙色 - 自适应方法
    h4(3).FaceColor = [0.2, 0.8, 0.2];  % 绿色 - GP-DDPG
    
    % 添加数值标签
    for i = 1:length(scenarios)
        for j = 1:3
            if rmse_values(i, j) > 0
                text(i + (j-2)*0.27, rmse_values(i, j) + max(rmse_values(:))*0.02, ...
                     sprintf('%.2f', rmse_values(i, j)), ...
                     'HorizontalAlignment', 'center', 'FontSize', 8);
            end
        end
    end
    
    set(gca, 'XTickLabel', scenarios);
    ylabel('RMSE (N)');
    title('跟踪精度RMSE对比');
    legend(methods, 'Location', 'best');
    grid on;
    
    % 综合性能评分
    subplot(2, 3, 5);
    performance_scores = zeros(length(scenarios), 3);
    
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        % 综合评分 = 1/(力误差+1) * 1/(调节时间+1) * 1/(RMSE+1) * 1000
        performance_scores(i, 1) = 1000 / ((comparison_results.(scenario).traditional.final_force_error + 1) * ...
                                          (comparison_results.(scenario).traditional.settling_time + 1) * ...
                                          (comparison_results.(scenario).traditional.rmse + 1));
        performance_scores(i, 2) = 1000 / ((comparison_results.(scenario).adaptive.final_force_error + 1) * ...
                                          (comparison_results.(scenario).adaptive.settling_time + 1) * ...
                                          (comparison_results.(scenario).adaptive.rmse + 1));
        performance_scores(i, 3) = 1000 / ((comparison_results.(scenario).ddpg.final_force_error + 1) * ...
                                          (comparison_results.(scenario).ddpg.settling_time + 1) * ...
                                          (comparison_results.(scenario).ddpg.rmse + 1));
    end
    
    % 创建柱状图并设置颜色
    h5 = bar(performance_scores);
    h5(1).FaceColor = [0.3, 0.7, 0.9];  % 浅蓝色 - 传统方法
    h5(2).FaceColor = [0.9, 0.6, 0.3];  % 橙色 - 自适应方法
    h5(3).FaceColor = [0.2, 0.8, 0.2];  % 绿色 - GP-DDPG
    
    % 添加数值标签
    for i = 1:length(scenarios)
        for j = 1:3
            if performance_scores(i, j) > 0
                text(i + (j-2)*0.27, performance_scores(i, j) + max(performance_scores(:))*0.02, ...
                     sprintf('%.1f', performance_scores(i, j)), ...
                     'HorizontalAlignment', 'center', 'FontSize', 8);
            end
        end
    end
    
    set(gca, 'XTickLabel', scenarios);
    ylabel('综合性能评分');
    title('综合性能对比');
    legend(methods, 'Location', 'best');
    grid on;
    
    % 训练收敛曲线
    subplot(2, 3, 6);
    if isfield(comparison_results, 'training_history') && ~isempty(comparison_results.training_history)
        plot(comparison_results.training_history.episode_rewards, 'b-', 'LineWidth', 2);
        xlabel('训练轮次');
        ylabel('累积奖励');
        title('GP-DDPG训练收敛过程');
        grid on;
    end
    
    sgtitle('优化后的GP-DDPG导纳控制性能对比分析', 'FontSize', 16, 'FontWeight', 'bold');
    
    % 保存图形
    if config.save_results
        timestamp = datestr(now, 'yyyymmdd_HHMMSS');
        filename = sprintf('optimized_ddpg_comparison_%s.png', timestamp);
        print('-dpng', '-r300', filename);
        fprintf('优化后的对比结果图已保存: %s\n', filename);
    end
end

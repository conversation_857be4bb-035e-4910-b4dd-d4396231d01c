classdef ddpg_networks < handle
    % DDPG网络结构定义类
    % 包含Actor网络和Critic网络的创建和管理
    
    properties
        actor_net           % Actor网络
        critic_net          % Critic网络
        target_actor_net    % 目标Actor网络
        target_critic_net   % 目标Critic网络
        
        state_dim           % 状态维度
        action_dim          % 动作维度
        
        % 网络参数
        actor_lr = 1e-4     % Actor学习率
        critic_lr = 1e-3    % Critic学习率
        tau = 0.005         % 软更新参数
        
        % 网络结构参数
        hidden_units = [128, 64]  % 隐藏层单元数
    end
    
    methods
        function obj = ddpg_networks(state_dim, action_dim, varargin)
            % 构造函数
            % 输入：
            %   state_dim - 状态空间维度
            %   action_dim - 动作空间维度
            %   varargin - 可选参数
            
            obj.state_dim = state_dim;
            obj.action_dim = action_dim;
            
            % 解析可选参数
            p = inputParser;
            addParameter(p, 'actor_lr', 1e-4);
            addParameter(p, 'critic_lr', 1e-3);
            addParameter(p, 'tau', 0.005);
            addParameter(p, 'hidden_units', [128, 64]);
            parse(p, varargin{:});
            
            obj.actor_lr = p.Results.actor_lr;
            obj.critic_lr = p.Results.critic_lr;
            obj.tau = p.Results.tau;
            obj.hidden_units = p.Results.hidden_units;
            
            % 创建网络
            obj.create_networks();
        end
        
        function create_networks(obj)
            % 创建Actor和Critic网络
            
            % Actor网络 - 输入状态，输出动作
            actor_layers = [
                featureInputLayer(obj.state_dim, 'Name', 'state_input')
                fullyConnectedLayer(obj.hidden_units(1), 'Name', 'actor_fc1')
                reluLayer('Name', 'actor_relu1')
                fullyConnectedLayer(obj.hidden_units(2), 'Name', 'actor_fc2')
                reluLayer('Name', 'actor_relu2')
                fullyConnectedLayer(obj.action_dim, 'Name', 'actor_output')
                tanhLayer('Name', 'actor_tanh')  % 动作范围[-1, 1]
            ];
            
            obj.actor_net = layerGraph(actor_layers);
            
            % Critic网络 - 输入状态和动作，输出Q值
            state_input = featureInputLayer(obj.state_dim, 'Name', 'critic_state_input');
            action_input = featureInputLayer(obj.action_dim, 'Name', 'critic_action_input');
            
            % 状态处理分支
            state_fc1 = fullyConnectedLayer(obj.hidden_units(1), 'Name', 'critic_state_fc1');
            state_relu1 = reluLayer('Name', 'critic_state_relu1');
            
            % 动作处理分支
            action_fc1 = fullyConnectedLayer(obj.hidden_units(1), 'Name', 'critic_action_fc1');
            
            % 合并层
            concat_layer = concatenationLayer(1, 2, 'Name', 'critic_concat');
            
            % 后续层
            fc2 = fullyConnectedLayer(obj.hidden_units(2), 'Name', 'critic_fc2');
            relu2 = reluLayer('Name', 'critic_relu2');
            output_layer = fullyConnectedLayer(1, 'Name', 'critic_output');
            
            % 构建Critic网络图
            critic_layers = [
                state_input
                state_fc1
                state_relu1
            ];
            
            obj.critic_net = layerGraph(critic_layers);
            obj.critic_net = addLayers(obj.critic_net, [action_input, action_fc1]);
            obj.critic_net = addLayers(obj.critic_net, [concat_layer, fc2, relu2, output_layer]);
            
            % 连接层
            obj.critic_net = connectLayers(obj.critic_net, 'critic_state_relu1', 'critic_concat/in1');
            obj.critic_net = connectLayers(obj.critic_net, 'critic_action_fc1', 'critic_concat/in2');
            
            % 创建目标网络（初始权重相同）
            obj.target_actor_net = obj.actor_net;
            obj.target_critic_net = obj.critic_net;
        end
        
        function action = get_action(obj, state, add_noise)
            % 获取Actor网络的动作输出
            % 输入：
            %   state - 当前状态
            %   add_noise - 是否添加噪声（用于探索）
            
            if nargin < 3
                add_noise = false;
            end
            
            % 确保状态是列向量
            if size(state, 1) == 1
                state = state';
            end
            
            % 通过Actor网络获取动作
            action = predict(obj.actor_net, state);
            
            % 添加噪声进行探索
            if add_noise
                noise = 0.1 * randn(size(action));  % 高斯噪声
                action = action + noise;
            end
            
            % 限制动作范围在[-1, 1]
            action = max(-1, min(1, action));
        end
        
        function q_value = get_q_value(obj, state, action, use_target)
            % 获取Critic网络的Q值
            % 输入：
            %   state - 状态
            %   action - 动作
            %   use_target - 是否使用目标网络
            
            if nargin < 4
                use_target = false;
            end
            
            % 选择使用的网络
            if use_target
                net = obj.target_critic_net;
            else
                net = obj.critic_net;
            end
            
            % 准备输入数据
            input_data = {state, action};
            
            % 获取Q值
            q_value = predict(net, input_data);
        end
        
        function soft_update_targets(obj)
            % 软更新目标网络
            % θ_target = τ * θ + (1 - τ) * θ_target
            
            % 更新目标Actor网络
            obj.target_actor_net = obj.soft_update_network(obj.actor_net, obj.target_actor_net);
            
            % 更新目标Critic网络
            obj.target_critic_net = obj.soft_update_network(obj.critic_net, obj.target_critic_net);
        end
        
        function target_net = soft_update_network(obj, source_net, target_net)
            % 执行单个网络的软更新
            % 这是一个简化版本，实际实现需要访问网络权重
            
            % 注意：在实际的MATLAB实现中，需要使用更复杂的方法来访问和更新网络权重
            % 这里提供一个概念性的实现框架
            
            fprintf('执行网络软更新，tau = %.4f\n', obj.tau);
            % 实际实现需要遍历所有层的权重和偏置进行更新
        end
    end
end

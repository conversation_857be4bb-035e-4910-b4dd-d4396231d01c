classdef replay_buffer < handle
    % 经验回放缓冲区类
    % 用于存储和采样DDPG训练所需的经验数据
    
    properties
        buffer_size         % 缓冲区大小
        batch_size          % 批次大小
        
        states              % 状态缓冲区
        actions             % 动作缓冲区
        rewards             % 奖励缓冲区
        next_states         % 下一状态缓冲区
        dones               % 终止标志缓冲区
        
        ptr                 % 当前指针位置
        size                % 当前缓冲区大小
        
        state_dim           % 状态维度
        action_dim          % 动作维度
    end
    
    methods
        function obj = replay_buffer(state_dim, action_dim, buffer_size, batch_size)
            % 构造函数
            % 输入：
            %   state_dim - 状态空间维度
            %   action_dim - 动作空间维度
            %   buffer_size - 缓冲区最大容量
            %   batch_size - 采样批次大小
            
            obj.state_dim = state_dim;
            obj.action_dim = action_dim;
            obj.buffer_size = buffer_size;
            obj.batch_size = batch_size;
            
            % 初始化缓冲区
            obj.states = zeros(state_dim, buffer_size);
            obj.actions = zeros(action_dim, buffer_size);
            obj.rewards = zeros(1, buffer_size);
            obj.next_states = zeros(state_dim, buffer_size);
            obj.dones = false(1, buffer_size);
            
            obj.ptr = 1;
            obj.size = 0;
        end
        
        function add(obj, state, action, reward, next_state, done)
            % 添加经验到缓冲区
            % 输入：
            %   state - 当前状态
            %   action - 执行的动作
            %   reward - 获得的奖励
            %   next_state - 下一状态
            %   done - 是否终止
            
            % 确保输入是列向量
            if size(state, 1) == 1
                state = state';
            end
            if size(next_state, 1) == 1
                next_state = next_state';
            end
            if size(action, 1) == 1
                action = action';
            end
            
            % 存储经验
            obj.states(:, obj.ptr) = state;
            obj.actions(:, obj.ptr) = action;
            obj.rewards(obj.ptr) = reward;
            obj.next_states(:, obj.ptr) = next_state;
            obj.dones(obj.ptr) = done;
            
            % 更新指针和大小
            obj.ptr = mod(obj.ptr, obj.buffer_size) + 1;
            obj.size = min(obj.size + 1, obj.buffer_size);
        end
        
        function [states, actions, rewards, next_states, dones] = sample(obj)
            % 从缓冲区随机采样一个批次的经验
            % 输出：
            %   states - 状态批次
            %   actions - 动作批次
            %   rewards - 奖励批次
            %   next_states - 下一状态批次
            %   dones - 终止标志批次
            
            if obj.size < obj.batch_size
                error('缓冲区中的经验数量不足以进行采样');
            end
            
            % 随机选择索引
            indices = randperm(obj.size, obj.batch_size);
            
            % 采样数据
            states = obj.states(:, indices);
            actions = obj.actions(:, indices);
            rewards = obj.rewards(indices);
            next_states = obj.next_states(:, indices);
            dones = obj.dones(indices);
        end
        
        function ready = is_ready(obj)
            % 检查缓冲区是否准备好进行采样
            ready = obj.size >= obj.batch_size;
        end
        
        function clear(obj)
            % 清空缓冲区
            obj.ptr = 1;
            obj.size = 0;
            
            % 重置所有缓冲区
            obj.states(:) = 0;
            obj.actions(:) = 0;
            obj.rewards(:) = 0;
            obj.next_states(:) = 0;
            obj.dones(:) = false;
        end
        
        function info = get_info(obj)
            % 获取缓冲区信息
            info.current_size = obj.size;
            info.buffer_size = obj.buffer_size;
            info.batch_size = obj.batch_size;
            info.utilization = obj.size / obj.buffer_size;
            info.ready_for_sampling = obj.is_ready();
        end
        
        function print_info(obj)
            % 打印缓冲区状态信息
            info = obj.get_info();
            fprintf('=== 经验回放缓冲区状态 ===\n');
            fprintf('当前大小: %d / %d (%.1f%%)\n', ...
                info.current_size, info.buffer_size, info.utilization * 100);
            fprintf('批次大小: %d\n', info.batch_size);
            fprintf('准备采样: %s\n', string(info.ready_for_sampling));
            fprintf('========================\n');
        end
        
        function save_buffer(obj, filename)
            % 保存缓冲区到文件
            buffer_data.states = obj.states(:, 1:obj.size);
            buffer_data.actions = obj.actions(:, 1:obj.size);
            buffer_data.rewards = obj.rewards(1:obj.size);
            buffer_data.next_states = obj.next_states(:, 1:obj.size);
            buffer_data.dones = obj.dones(1:obj.size);
            buffer_data.size = obj.size;
            buffer_data.state_dim = obj.state_dim;
            buffer_data.action_dim = obj.action_dim;
            
            save(filename, 'buffer_data');
            fprintf('缓冲区已保存到: %s\n', filename);
        end
        
        function load_buffer(obj, filename)
            % 从文件加载缓冲区
            if ~exist(filename, 'file')
                error('文件不存在: %s', filename);
            end
            
            loaded = load(filename);
            buffer_data = loaded.buffer_data;
            
            % 验证维度匹配
            if buffer_data.state_dim ~= obj.state_dim || ...
               buffer_data.action_dim ~= obj.action_dim
                error('加载的缓冲区维度与当前设置不匹配');
            end
            
            % 加载数据
            load_size = min(buffer_data.size, obj.buffer_size);
            obj.states(:, 1:load_size) = buffer_data.states(:, 1:load_size);
            obj.actions(:, 1:load_size) = buffer_data.actions(:, 1:load_size);
            obj.rewards(1:load_size) = buffer_data.rewards(1:load_size);
            obj.next_states(:, 1:load_size) = buffer_data.next_states(:, 1:load_size);
            obj.dones(1:load_size) = buffer_data.dones(1:load_size);
            
            obj.size = load_size;
            obj.ptr = mod(load_size, obj.buffer_size) + 1;
            
            fprintf('缓冲区已从文件加载: %s (大小: %d)\n', filename, load_size);
        end
    end
end

classdef policy_switcher < handle
    % 策略切换器类
    % 实现传统自适应阻抗控制和DDPG控制之间的智能切换
    % 基于论文公式(16)的策略切换逻辑
    
    properties
        % 切换参数
        alpha1_initial = 0.5    % 进一步降低初始传统控制概率
        alpha1_decay = 0.95     % 加快衰减速度
        alpha1_min = 0.02       % 更低的最小概率
        epsilon = 0.01          % 更低的切换阈值
        delta = 1.0             % 更小的力误差邻域半径

        % 强制探索参数
        force_exploration_interval = 10  % 每10轮强制探索
        force_exploration_duration = 3   % 强制探索持续3轮
        exploration_counter = 0          % 探索计数器
        episode = 0                      % 当前轮次

        % 统计信息
        total_steps = 0                  % 总步数
        adaptive_steps = 0               % 传统控制步数
        ddpg_steps = 0                   % DDPG控制步数
        mixed_steps = 0                  % 混合控制步数
        
        % 当前状态
        current_alpha1          % 当前alpha1值
        training_episode = 0    % 训练轮数
        
        % 策略对象
        adaptive_controller     % 传统自适应控制器
        ddpg_controller        % DDPG控制器
        
        % 切换历史记录
        switch_history = []     % 切换历史：1-传统控制，2-DDPG控制
        performance_history = []% 性能历史
        
        % 性能评估参数
        performance_window = 50 % 性能评估窗口
        force_error_threshold = 1.0  % 力误差阈值
        stability_threshold = 0.5    % 稳定性阈值
        
        % 平滑切换参数
        smooth_transition = true     % 是否启用平滑切换
        transition_weight = 0.0      % 过渡权重 [0,1]
        transition_duration = 20     % 过渡持续时间步数
        transition_counter = 0       % 过渡计数器
    end
    
    methods
        function obj = policy_switcher(adaptive_controller, ddpg_controller, varargin)
            % 构造函数
            % 输入：
            %   adaptive_controller - 传统自适应控制器
            %   ddpg_controller - DDPG控制器
            %   varargin - 可选参数
            
            obj.adaptive_controller = adaptive_controller;
            obj.ddpg_controller = ddpg_controller;
            obj.current_alpha1 = obj.alpha1_initial;
            
            % 解析可选参数
            p = inputParser;
            addParameter(p, 'alpha1_initial', 0.9);
            addParameter(p, 'alpha1_decay', 0.95);
            addParameter(p, 'alpha1_min', 0.1);
            addParameter(p, 'epsilon', 0.05);
            addParameter(p, 'delta', 2.0);
            addParameter(p, 'smooth_transition', true);
            addParameter(p, 'transition_duration', 20);
            parse(p, varargin{:});
            
            obj.alpha1_initial = p.Results.alpha1_initial;
            obj.alpha1_decay = p.Results.alpha1_decay;
            obj.alpha1_min = p.Results.alpha1_min;
            obj.epsilon = p.Results.epsilon;
            obj.delta = p.Results.delta;
            obj.smooth_transition = p.Results.smooth_transition;
            obj.transition_duration = p.Results.transition_duration;
            
            fprintf('策略切换器初始化完成\n');
        end
        
        function [action, policy_used] = select_action(obj, state, force_error)
            % 选择动作和策略
            % 基于论文公式(16)的策略选择逻辑
            % 输入：
            %   state - 当前状态
            %   force_error - 力误差 (delta_F)
            % 输出：
            %   action - 选择的动作
            %   policy_used - 使用的策略 (1-传统, 2-DDPG, 3-混合)

            % 更新探索计数器
            obj.exploration_counter = obj.exploration_counter + 1;

            % 强制探索机制
            force_ddpg = false;
            if mod(obj.exploration_counter, obj.force_exploration_interval) <= obj.force_exploration_duration
                force_ddpg = true;
                if mod(obj.exploration_counter, obj.force_exploration_interval) == 1
                    fprintf('开始强制探索DDPG策略 (第%d轮)\n', obj.episode);
                end
            end

            % 判断是否在力误差邻域内
            in_force_neighborhood = abs(force_error) <= obj.delta;

            % 判断是否应该使用传统控制（考虑强制探索）
            if force_ddpg
                use_adaptive = false;  % 强制使用DDPG
            else
                use_adaptive = (obj.current_alpha1 >= obj.epsilon) || in_force_neighborhood;
            end
            
            if obj.smooth_transition && obj.transition_counter > 0
                % 平滑过渡模式
                [action, policy_used] = obj.smooth_transition_action(state);
            elseif use_adaptive
                % 使用传统自适应控制
                action = obj.get_adaptive_action(state, force_error);
                policy_used = 1;
                obj.switch_history = [obj.switch_history, 1];
            else
                % 使用DDPG控制
                action = obj.ddpg_controller.get_action(state, true);
                policy_used = 2;
                obj.switch_history = [obj.switch_history, 2];
                
                % 检查是否需要启动平滑过渡
                if obj.smooth_transition && obj.should_start_transition(state, force_error)
                    obj.start_smooth_transition();
                end
            end
            
            % 记录性能
            obj.record_performance(state, action, force_error, policy_used);
        end
        
        function action = get_adaptive_action(obj, state, force_error)
            % 获取传统自适应控制动作
            % 基于论文公式(15)的自适应控制律

            % 解析状态
            Xc = state(1);
            Xc_dot = state(2);
            Fe = state(3);
            Fd = state(4);

            % 改进的自适应控制参数
            kI = 0.1;  % 小的刚度参数提供稳定性

            % 自适应阻尼参数计算
            % 基于论文公式(15)的改进版本
            b_first = 120;  % 增加初始阻尼值
            sigma = 0.01;   % 增加学习率
            lambda = 1e-6;  % 调整采样率

            % 改进的自适应律
            % 考虑力误差的符号和大小
            if abs(force_error) > 0.1
                delta_b = sigma * force_error * Xc_dot * exp(-abs(force_error)/10);
            else
                delta_b = 0;  % 小误差时不调整
            end

            bI = b_first + delta_b;

            % 添加速度相关的阻尼调整
            velocity_damping = 20 * abs(Xc_dot);
            bI = bI + velocity_damping;

            % 限制参数范围
            bI = max(50, min(300, bI));   % 提高最小阻尼值
            kI = max(0, min(10, kI));

            action = [bI; kI];
        end
        
        function [action, policy_used] = smooth_transition_action(obj, state)
            % 平滑过渡动作
            % 在两种策略之间进行加权平均
            
            % 获取两种策略的动作
            adaptive_action = obj.get_adaptive_action(state, state(5));
            ddpg_action = obj.ddpg_controller.get_action(state, false);
            
            % 计算过渡权重
            progress = obj.transition_counter / obj.transition_duration;
            weight_ddpg = progress;
            weight_adaptive = 1 - progress;
            
            % 加权平均
            action = weight_adaptive * adaptive_action + weight_ddpg * ddpg_action;
            policy_used = 3;  % 混合策略
            
            % 更新过渡计数器
            obj.transition_counter = obj.transition_counter - 1;
            if obj.transition_counter <= 0
                obj.transition_counter = 0;
            end
            
            obj.switch_history = [obj.switch_history, 3];
        end
        
        function should_start = should_start_transition(obj, state, force_error)
            % 判断是否应该开始平滑过渡
            
            % 检查系统稳定性
            velocity = abs(state(2));
            acceleration_estimate = abs(force_error) / 1.0;  % 简化的加速度估计
            
            % 如果系统接近稳定状态，启动过渡
            should_start = (velocity < obj.stability_threshold) && ...
                          (acceleration_estimate < obj.stability_threshold) && ...
                          (abs(force_error) < obj.force_error_threshold);
        end
        
        function start_smooth_transition(obj)
            % 启动平滑过渡
            obj.transition_counter = obj.transition_duration;
            fprintf('启动平滑过渡，持续时间：%d步\n', obj.transition_duration);
        end
        
        function update_alpha1(obj)
            % 更新alpha1参数
            % 随着训练进行，逐渐减少选择传统控制的概率

            obj.training_episode = obj.training_episode + 1;
            obj.episode = obj.training_episode;  % 同步episode属性

            % 指数衰减
            obj.current_alpha1 = max(obj.alpha1_min, ...
                                   obj.alpha1_initial * (obj.alpha1_decay ^ obj.training_episode));

            fprintf('训练轮次：%d，当前alpha1：%.4f\n', obj.training_episode, obj.current_alpha1);
        end
        
        function record_performance(obj, state, action, force_error, policy_used)
            % 记录性能指标

            % 改进的性能指标计算
            force_tracking_error = abs(force_error);
            velocity_error = abs(state(2));  % 速度稳定性
            position_error = abs(state(6));  % 位置误差

            % 使用指数函数提高敏感性
            force_performance = exp(-force_tracking_error / 5);
            velocity_performance = exp(-velocity_error / 2);
            position_performance = exp(-position_error / 3);

            % 综合性能指标（加权平均）
            performance = 0.6 * force_performance + 0.3 * velocity_performance + 0.1 * position_performance;

            obj.performance_history = [obj.performance_history, performance];

            % 更新策略使用统计
            obj.total_steps = obj.total_steps + 1;
            if policy_used == 1
                obj.adaptive_steps = obj.adaptive_steps + 1;
            elseif policy_used == 2
                obj.ddpg_steps = obj.ddpg_steps + 1;
            else
                obj.mixed_steps = obj.mixed_steps + 1;
            end

            % 保持历史记录长度
            if length(obj.performance_history) > 1000
                obj.performance_history = obj.performance_history(end-999:end);
                obj.switch_history = obj.switch_history(end-999:end);
            end
        end
        
        function performance = evaluate_recent_performance(obj)
            % 评估最近的性能
            
            if length(obj.performance_history) < obj.performance_window
                performance = 0;
                return;
            end
            
            recent_performance = obj.performance_history(end-obj.performance_window+1:end);
            performance = mean(recent_performance);
        end
        
        function stats = get_switching_statistics(obj)
            % 获取切换统计信息
            
            if isempty(obj.switch_history)
                stats = struct();
                return;
            end
            
            total_steps = length(obj.switch_history);
            adaptive_steps = sum(obj.switch_history == 1);
            ddpg_steps = sum(obj.switch_history == 2);
            mixed_steps = sum(obj.switch_history == 3);
            
            stats.total_steps = total_steps;
            stats.adaptive_ratio = adaptive_steps / total_steps;
            stats.ddpg_ratio = ddpg_steps / total_steps;
            stats.mixed_ratio = mixed_steps / total_steps;
            stats.current_alpha1 = obj.current_alpha1;
            stats.training_episode = obj.training_episode;
            stats.recent_performance = obj.evaluate_recent_performance();
        end
        
        function plot_switching_history(obj)
            % 绘制策略切换历史
            
            if isempty(obj.switch_history)
                warning('没有切换历史数据');
                return;
            end
            
            figure;
            
            % 策略使用历史
            subplot(3, 1, 1);
            plot(obj.switch_history, 'o-', 'LineWidth', 1.5, 'MarkerSize', 3);
            ylabel('策略类型');
            title('策略切换历史');
            ylim([0.5, 3.5]);
            yticks([1, 2, 3]);
            yticklabels({'传统自适应', 'DDPG', '混合'});
            grid on;
            
            % 性能历史
            subplot(3, 1, 2);
            plot(obj.performance_history, 'b-', 'LineWidth', 1.5);
            ylabel('性能指标');
            title('性能变化历史');
            grid on;
            
            % Alpha1变化
            subplot(3, 1, 3);
            alpha1_history = obj.alpha1_initial * (obj.alpha1_decay .^ (0:obj.training_episode));
            plot(0:obj.training_episode, alpha1_history, 'r-', 'LineWidth', 2);
            xlabel('训练轮次');
            ylabel('Alpha1值');
            title('策略选择概率变化');
            grid on;
        end
        
        function print_status(obj)
            % 打印切换器状态
            
            stats = obj.get_switching_statistics();
            
            fprintf('=== 策略切换器状态 ===\n');
            fprintf('训练轮次: %d\n', obj.training_episode);
            fprintf('当前Alpha1: %.4f\n', obj.current_alpha1);
            fprintf('总步数: %d\n', stats.total_steps);
            fprintf('传统控制比例: %.2f%%\n', stats.adaptive_ratio * 100);
            fprintf('DDPG控制比例: %.2f%%\n', stats.ddpg_ratio * 100);
            fprintf('混合控制比例: %.2f%%\n', stats.mixed_ratio * 100);
            fprintf('最近性能: %.4f\n', stats.recent_performance);
            if obj.transition_counter > 0
                fprintf('过渡状态: %d/%d\n', obj.transition_counter, obj.transition_duration);
            end
            fprintf('====================\n');
        end
        
        function reset(obj)
            % 重置切换器状态
            
            obj.current_alpha1 = obj.alpha1_initial;
            obj.training_episode = 0;
            obj.switch_history = [];
            obj.performance_history = [];
            obj.transition_counter = 0;
            
            fprintf('策略切换器已重置\n');
        end
    end
end

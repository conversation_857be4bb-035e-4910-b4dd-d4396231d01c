% 测试优化后的GP-DDPG系统
clear all;
close all;
clc;
addpath(pwd);

fprintf('=== 测试优化后的GP-DDPG系统 ===\n');

try
    % 设置随机种子
    rng(42);
    
    % 第一阶段：验证策略切换机制
    fprintf('\n1. 验证策略切换机制...\n');
    test_policy_switching();
    
    % 第二阶段：测试场景自适应性
    fprintf('\n2. 测试场景自适应性...\n');
    test_scenario_adaptation();
    
    % 第三阶段：验证收敛判断
    fprintf('\n3. 验证收敛判断...\n');
    test_convergence_detection();
    
    % 第四阶段：完整性能对比
    fprintf('\n4. 完整性能对比...\n');
    run_complete_comparison();
    
    fprintf('\n=== 所有测试完成 ===\n');
    
catch ME
    fprintf('测试失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

function test_policy_switching()
    % 测试策略切换机制
    
    fprintf('  创建优化器进行策略切换测试...\n');
    optimizer = admittance_optimizer('max_episodes', 20, ...
                                   'max_steps_per_episode', 200, ...
                                   'plot_interval', 5);
    
    % 训练并监控策略使用情况
    optimizer.train();
    
    % 检查策略使用统计
    switcher = optimizer.policy_switcher;
    total_steps = switcher.total_steps;
    ddpg_ratio = switcher.ddpg_steps / total_steps * 100;
    adaptive_ratio = switcher.adaptive_steps / total_steps * 100;
    
    fprintf('  策略使用统计:\n');
    fprintf('    DDPG使用率: %.1f%%\n', ddpg_ratio);
    fprintf('    传统控制使用率: %.1f%%\n', adaptive_ratio);
    
    if ddpg_ratio > 10
        fprintf('  ✅ 策略切换机制工作正常\n');
    else
        fprintf('  ⚠️ DDPG使用率仍然较低\n');
    end
end

function test_scenario_adaptation()
    % 测试场景自适应性
    
    scenarios = {'step', 'variable_stiffness', 'sinusoidal'};
    
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        fprintf('  测试场景: %s\n', scenario);
        
        % 创建环境并测试奖励函数
        env = force_control_environment();
        env.current_scenario = scenario;
        
        % 测试不同的状态和动作
        test_states = [
            [1.0, 0.1, 15, 20, 5, 0.1];   % 一般状态
            [1.0, 0.5, 18, 20, 2, 0.05];  % 接近目标
            [1.0, 0.01, 19.5, 20, 0.5, 0.02]; % 很接近目标
        ];
        
        test_actions = [
            [100, 1];  % 中等参数
            [150, 2];  % 高阻尼
            [80, 0.5]; % 低阻尼
        ];
        
        rewards = [];
        for j = 1:size(test_states, 1)
            for k = 1:size(test_actions, 1)
                state = test_states(j, :)';
                action = test_actions(k, :)';
                env.current_state = state;
                
                reward = env.calculate_reward(action, state(5), state(2));
                rewards = [rewards, reward];
            end
        end
        
        fprintf('    奖励范围: [%.1f, %.1f]\n', min(rewards), max(rewards));
        
        % 检查奖励是否合理
        if all(isfinite(rewards)) && max(rewards) > min(rewards)
            fprintf('    ✅ 场景自适应奖励正常\n');
        else
            fprintf('    ⚠️ 奖励函数可能有问题\n');
        end
    end
end

function test_convergence_detection()
    % 测试收敛判断
    
    fprintf('  创建优化器进行收敛测试...\n');
    optimizer = admittance_optimizer('max_episodes', 30, ...
                                   'max_steps_per_episode', 300, ...
                                   'plot_interval', 10);
    
    % 模拟训练过程
    optimizer.train();
    
    % 检查收敛检测
    if length(optimizer.episode_rewards) > 20
        converged = optimizer.check_convergence();
        fprintf('  收敛状态: %s\n', string(converged));
        
        if converged
            fprintf('  ✅ 收敛检测正常工作\n');
        else
            fprintf('  ℹ️ 系统尚未收敛（正常情况）\n');
        end
    else
        fprintf('  ℹ️ 训练轮数不足，无法测试收敛\n');
    end
    
    % 测试调节时间计算
    test_scenarios = {'step', 'variable_stiffness', 'sinusoidal'};
    for i = 1:length(test_scenarios)
        scenario = test_scenarios{i};
        optimizer.current_scenario = scenario;
        
        % 模拟力误差序列
        force_errors = [10, 8, 6, 4, 3, 2, 1.5, 1.2, 1.0, 0.8, 0.9, 1.1, 0.7, 0.8, 0.6];
        force_errors = [force_errors, 0.5 * ones(1, 100)];  % 稳定阶段
        
        settling_time = optimizer.calculate_settling_time(force_errors);
        
        fprintf('  场景 %s 调节时间: %.3f s\n', scenario, settling_time);
        
        if isfinite(settling_time)
            fprintf('    ✅ 调节时间计算正常\n');
        else
            fprintf('    ⚠️ 调节时间计算可能有问题\n');
        end
    end
end

function run_complete_comparison()
    % 运行完整的性能对比
    
    fprintf('  开始完整性能对比测试...\n');
    
    % 创建优化器
    optimizer = admittance_optimizer('max_episodes', 50, ...
                                   'max_steps_per_episode', 800, ...
                                   'plot_interval', 10);
    
    % 训练
    tic;
    optimizer.train();
    training_time = toc;
    
    fprintf('  训练完成，用时: %.1f秒\n', training_time);
    
    % 测试所有场景
    scenarios = {'sinusoidal', 'step', 'variable_stiffness'};
    results = struct();
    
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        fprintf('  测试场景: %s\n', scenario);
        
        % 测试GP-DDPG
        gp_ddpg_result = optimizer.test_performance(scenario);
        
        % 测试传统方法
        traditional_result = test_traditional_control(scenario);
        
        % 保存结果
        results.(scenario) = struct();
        results.(scenario).gp_ddpg = gp_ddpg_result;
        results.(scenario).traditional = traditional_result;
        
        % 计算改善
        force_improvement = (traditional_result.final_force_error - gp_ddpg_result.final_force_error) / traditional_result.final_force_error * 100;
        
        if isfinite(gp_ddpg_result.settling_time) && isfinite(traditional_result.settling_time)
            time_improvement = (traditional_result.settling_time - gp_ddpg_result.settling_time) / traditional_result.settling_time * 100;
        else
            time_improvement = NaN;
        end
        
        fprintf('    GP-DDPG: 力误差=%.3f, 调节时间=%.3f\n', ...
                gp_ddpg_result.final_force_error, gp_ddpg_result.settling_time);
        fprintf('    传统方法: 力误差=%.3f, 调节时间=%.3f\n', ...
                traditional_result.final_force_error, traditional_result.settling_time);
        fprintf('    改善: 力误差%.1f%%, 调节时间%.1f%%\n', force_improvement, time_improvement);
    end
    
    % 保存结果
    timestamp = datestr(now, 'yyyymmdd_HHMMSS');
    filename = sprintf('optimized_system_results_%s.mat', timestamp);
    save(filename, 'results', 'training_time');
    fprintf('  结果已保存到: %s\n', filename);
end

function result = test_traditional_control(scenario)
    % 测试传统控制方法
    
    env = force_control_environment();
    state = env.reset();
    
    % 固定参数
    B_fixed = 120;  % 改进的固定阻尼
    K_fixed = 0.5;  % 小的刚度
    
    states_history = [];
    for step = 1:800
        env.set_environment_variation(scenario);
        action = [B_fixed; K_fixed];
        [next_state, ~, done] = env.step(action);
        states_history = [states_history, state];
        state = next_state;
        if done, break; end
    end
    
    % 计算性能指标
    force_errors = abs(states_history(5, :));
    
    result = struct();
    result.final_force_error = force_errors(end);
    result.max_force_error = max(force_errors);
    
    % 计算调节时间
    threshold = 2.0;
    stable_duration = 50;
    settling_time = inf;
    
    for i = stable_duration:length(force_errors)
        if all(force_errors(i-stable_duration+1:i) < threshold)
            settling_time = (i - stable_duration) * env.dt;
            break;
        end
    end
    
    result.settling_time = settling_time;
end

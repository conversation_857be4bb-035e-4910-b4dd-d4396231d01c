% 运行改进的训练
clear all;
close all;
clc;
addpath(pwd);

% 设置随机种子
rng(123);

fprintf('=== 运行改进的GP-DDPG训练 ===\n');

try
    % 创建改进的优化器
    fprintf('创建优化器...\n');
    optimizer = admittance_optimizer('max_episodes', 80, ...
                                   'max_steps_per_episode', 1500, ...
                                   'plot_interval', 10);
    
    % 开始训练
    fprintf('开始训练...\n');
    tic;
    optimizer.train();
    training_time = toc;
    
    fprintf('训练完成，用时: %.1f秒\n', training_time);
    
    % 性能测试
    fprintf('开始性能测试...\n');
    scenarios = {'sinusoidal', 'step', 'variable_stiffness'};
    
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        fprintf('测试场景: %s\n', scenario);
        
        % 测试GP-DDPG
        ddpg_result = optimizer.test_performance(scenario);
        fprintf('  GP-DDPG: 力误差=%.3f, 调节时间=%.3f\n', ...
                ddpg_result.final_force_error, ddpg_result.settling_time);
    end
    
    % 绘制训练结果
    optimizer.plot_training_results();
    
    fprintf('改进训练完成！\n');
    
catch ME
    fprintf('训练失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

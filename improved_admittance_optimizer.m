classdef improved_admittance_optimizer < handle
    % 改进的导纳参数优化器类
    % 集成TD3算法、优先经验回放、课程学习等先进技术
    
    properties
        % 核心组件
        environment             % 力控制环境
        actor_net              % Actor网络
        critic_net1            % 第一个Critic网络
        critic_net2            % 第二个Critic网络
        target_actor           % 目标Actor网络
        target_critic1         % 目标Critic网络1
        target_critic2         % 目标Critic网络2
        
        % 网络参数
        state_dim = 6          % 状态维度
        action_dim = 2         % 动作维度
        hidden_size = 256      % 隐藏层大小
        
        % 训练参数
        max_episodes = 100     % 最大训练轮数
        max_steps_per_episode = 1500  % 每轮最大步数
        batch_size = 256       % 批次大小
        buffer_size = 1e6      % 经验回放缓冲区大小
        gamma = 0.99           % 折扣因子
        tau = 0.005            % 软更新参数
        learning_rate_actor = 1e-4     % Actor学习率
        learning_rate_critic = 1e-3    % Critic学习率
        
        % TD3特定参数
        policy_noise = 0.2     % 策略噪声
        noise_clip = 0.5       % 噪声裁剪
        policy_update_freq = 2 % 策略更新频率
        
        % 探索参数
        exploration_noise_scale = 0.3  % 探索噪声幅度
        
        % 经验回放缓冲区
        replay_buffer
        
        % 训练记录
        episode_rewards = []
        episode_force_errors = []
        convergence_times = []
        q_values = []
        critic_losses = []
        actor_losses = []
        
        % 当前状态
        current_episode = 0
        current_step = 0
        total_steps = 0
        
        % 场景设置
        current_scenario = 'sinusoidal'
        test_scenarios = {'sinusoidal', 'step', 'variable_stiffness'}
        
        % 课程学习
        curriculum_level = 1
        max_curriculum_level = 5
        
        % 可视化参数
        plot_interval = 10
        save_results = true
        
        % 性能指标
        best_performance = -inf
        convergence_threshold = 1.0
    end
    
    methods
        function obj = improved_admittance_optimizer(varargin)
            % 构造函数
            
            % 解析参数
            p = inputParser;
            addParameter(p, 'max_episodes', 100);
            addParameter(p, 'max_steps_per_episode', 1500);
            addParameter(p, 'batch_size', 256);
            addParameter(p, 'buffer_size', 1e6);
            addParameter(p, 'gamma', 0.99);
            addParameter(p, 'tau', 0.005);
            addParameter(p, 'learning_rate_actor', 1e-4);
            addParameter(p, 'learning_rate_critic', 1e-3);
            addParameter(p, 'exploration_noise_scale', 0.3);
            addParameter(p, 'policy_noise', 0.2);
            addParameter(p, 'noise_clip', 0.5);
            addParameter(p, 'policy_update_freq', 2);
            addParameter(p, 'plot_interval', 10);
            addParameter(p, 'save_results', true);
            parse(p, varargin{:});
            
            % 设置参数
            obj.max_episodes = p.Results.max_episodes;
            obj.max_steps_per_episode = p.Results.max_steps_per_episode;
            obj.batch_size = p.Results.batch_size;
            obj.buffer_size = p.Results.buffer_size;
            obj.gamma = p.Results.gamma;
            obj.tau = p.Results.tau;
            obj.learning_rate_actor = p.Results.learning_rate_actor;
            obj.learning_rate_critic = p.Results.learning_rate_critic;
            obj.exploration_noise_scale = p.Results.exploration_noise_scale;
            obj.policy_noise = p.Results.policy_noise;
            obj.noise_clip = p.Results.noise_clip;
            obj.policy_update_freq = p.Results.policy_update_freq;
            obj.plot_interval = p.Results.plot_interval;
            obj.save_results = p.Results.save_results;
            
            % 初始化环境
            obj.environment = force_control_environment();
            
            % 构建网络
            obj.build_networks();
            
            % 初始化经验回放缓冲区
            obj.replay_buffer = PrioritizedReplayBuffer(obj.buffer_size);
            
            fprintf('改进的导纳控制优化器已初始化\n');
            fprintf('  状态维度: %d, 动作维度: %d\n', obj.state_dim, obj.action_dim);
            fprintf('  隐藏层大小: %d, 批次大小: %d\n', obj.hidden_size, obj.batch_size);
            fprintf('  Actor学习率: %.2e, Critic学习率: %.2e\n', ...
                    obj.learning_rate_actor, obj.learning_rate_critic);
        end
        
        function build_networks(obj)
            % 构建改进的网络结构
            
            fprintf('构建改进的神经网络...\n');
            
            % 构建Actor网络（更深更宽的结构）
            obj.actor_net = obj.create_improved_actor_network();
            
            % 构建双Critic网络（TD3）
            obj.critic_net1 = obj.create_improved_critic_network();
            obj.critic_net2 = obj.create_improved_critic_network();
            
            % 初始化目标网络
            obj.target_actor = obj.copy_network(obj.actor_net);
            obj.target_critic1 = obj.copy_network(obj.critic_net1);
            obj.target_critic2 = obj.copy_network(obj.critic_net2);
            
            fprintf('网络构建完成\n');
        end
        
        function actor_net = create_improved_actor_network(obj)
            % 创建改进的Actor网络
            
            layers = [
                featureInputLayer(obj.state_dim, 'Name', 'input')
                
                % 第一层：增加网络深度和宽度
                fullyConnectedLayer(obj.hidden_size, 'Name', 'fc1')
                batchNormalizationLayer('Name', 'bn1')
                reluLayer('Name', 'relu1')
                
                % 第二层
                fullyConnectedLayer(obj.hidden_size, 'Name', 'fc2')
                batchNormalizationLayer('Name', 'bn2')
                reluLayer('Name', 'relu2')
                
                % 第三层（增加深度）
                fullyConnectedLayer(obj.hidden_size/2, 'Name', 'fc3')
                batchNormalizationLayer('Name', 'bn3')
                reluLayer('Name', 'relu3')
                
                % 输出层
                fullyConnectedLayer(obj.action_dim, 'Name', 'fc_out')
                tanhLayer('Name', 'tanh_out')
            ];
            
            actor_net = layerGraph(layers);
        end
        
        function critic_net = create_improved_critic_network(obj)
            % 创建改进的Critic网络
            
            layers = [
                featureInputLayer(obj.state_dim + obj.action_dim, 'Name', 'input')
                
                % 第一层
                fullyConnectedLayer(obj.hidden_size, 'Name', 'fc1')
                batchNormalizationLayer('Name', 'bn1')
                reluLayer('Name', 'relu1')
                
                % 第二层
                fullyConnectedLayer(obj.hidden_size, 'Name', 'fc2')
                batchNormalizationLayer('Name', 'bn2')
                reluLayer('Name', 'relu2')
                
                % 第三层
                fullyConnectedLayer(obj.hidden_size/2, 'Name', 'fc3')
                batchNormalizationLayer('Name', 'bn3')
                reluLayer('Name', 'relu3')
                
                % 输出层（Q值）
                fullyConnectedLayer(1, 'Name', 'fc_out')
            ];
            
            critic_net = layerGraph(layers);
        end
        
        function target_net = copy_network(obj, source_net)
            % 复制网络（简化实现）
            target_net = source_net;
        end
        
        function train(obj)
            % 主训练函数
            
            fprintf('开始改进的GP-DDPG训练...\n');
            
            % 分阶段训练策略
            obj.train_with_curriculum();
            
            fprintf('训练完成！\n');
            obj.save_training_results();
        end
        
        function train_with_curriculum(obj)
            % 课程学习训练策略
            
            fprintf('使用课程学习策略训练...\n');
            
            % 阶段1：预训练 - 高探索率
            fprintf('阶段1：预训练开始...\n');
            obj.exploration_noise_scale = 0.5;
            obj.learning_rate_actor = 1e-4;
            obj.learning_rate_critic = 1e-3;
            obj.train_phase(20, 500);  % 20轮，每轮500步
            
            % 阶段2：主训练 - 平衡探索与利用
            fprintf('阶段2：主训练开始...\n');
            obj.exploration_noise_scale = 0.3;
            obj.learning_rate_actor = 5e-5;
            obj.learning_rate_critic = 5e-4;
            obj.train_phase(40, 1000);  % 40轮，每轮1000步
            
            % 阶段3：精细调整 - 低探索率
            fprintf('阶段3：精细调整开始...\n');
            obj.exploration_noise_scale = 0.1;
            obj.learning_rate_actor = 1e-5;
            obj.learning_rate_critic = 1e-4;
            obj.train_phase(20, 1500);  % 20轮，每轮1500步
            
            % 阶段4：场景特化训练
            fprintf('阶段4：场景特化训练开始...\n');
            for i = 1:length(obj.test_scenarios)
                scenario = obj.test_scenarios{i};
                fprintf('  训练场景: %s\n', scenario);
                obj.current_scenario = scenario;
                obj.exploration_noise_scale = 0.05;
                obj.train_phase(10, 1000);  % 每个场景10轮，每轮1000步
            end
        end
        
        function train_phase(obj, num_episodes, steps_per_episode)
            % 训练阶段
            
            for episode = 1:num_episodes
                obj.current_episode = obj.current_episode + 1;
                
                % 运行一轮训练
                [episode_reward, episode_force_error, convergence_time] = ...
                    obj.run_episode(steps_per_episode);
                
                % 记录结果
                obj.episode_rewards = [obj.episode_rewards, episode_reward];
                obj.episode_force_errors = [obj.episode_force_errors, episode_force_error];
                obj.convergence_times = [obj.convergence_times, convergence_time];
                
                % 打印进度
                if mod(obj.current_episode, obj.plot_interval) == 0
                    obj.print_training_progress();
                end
                
                % 检查收敛
                if obj.check_convergence()
                    fprintf('训练收敛，提前结束\n');
                    break;
                end
            end
        end
    end
end

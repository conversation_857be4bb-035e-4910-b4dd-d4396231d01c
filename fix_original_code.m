% 修复原始代码中的subplot问题
% 为policy_switcher类添加一个新的方法，在当前子图中绘制

fprintf('创建policy_switcher类的修复版本...\n');

% 创建修复后的绘图方法
function plot_switching_history_in_subplot(switch_history, performance_history, training_episode, alpha1_initial, alpha1_decay)
    % 在当前子图中绘制策略切换历史（不创建新窗口）
    % 这是policy_switcher.plot_switching_history()的修复版本
    
    if isempty(switch_history)
        text(0.5, 0.5, '无策略切换数据', 'HorizontalAlignment', 'center', ...
             'Units', 'normalized', 'FontSize', 12);
        title('策略切换历史');
        return;
    end
    
    % 绘制策略切换历史
    plot(switch_history, 'o-', 'LineWidth', 1.5, 'MarkerSize', 4, ...
         'Color', [0.8, 0.4, 0.8], 'MarkerFaceColor', [0.8, 0.4, 0.8]);
    
    xlabel('时间步');
    ylabel('策略类型');
    title('策略切换历史');
    
    % 设置y轴
    ylim([0.5, 3.5]);
    yticks([1, 2, 3]);
    yticklabels({'传统自适应', 'GP-DDPG', '混合'});
    
    grid on;
    
    % 添加统计信息
    if ~isempty(switch_history)
        adaptive_ratio = sum(switch_history == 1) / length(switch_history) * 100;
        ddpg_ratio = sum(switch_history == 2) / length(switch_history) * 100;
        mixed_ratio = sum(switch_history == 3) / length(switch_history) * 100;
        
        % 在图上添加统计文本
        text_str = sprintf('传统: %.1f%%\nDDPG: %.1f%%\n混合: %.1f%%', ...
                          adaptive_ratio, ddpg_ratio, mixed_ratio);
        text(0.02, 0.98, text_str, 'Units', 'normalized', ...
             'VerticalAlignment', 'top', 'FontSize', 8, ...
             'BackgroundColor', 'white', 'EdgeColor', 'black');
    end
end

% 演示如何使用修复后的代码
fprintf('演示修复后的Figure 1...\n');

% 生成模拟数据
episodes = 150;
episode_rewards = linspace(-1.8, -0.6, episodes) + 0.1 * randn(1, episodes);
episode_force_errors = [10 * ones(1, 20), linspace(10, 6.5, 130)] + 0.3 * randn(1, episodes);
episode_convergence_times = [7.5 * ones(1, 20), linspace(7.5, 4.5, 130)] + 0.2 * randn(1, episodes);

% 生成策略切换历史
switch_history = ones(1, episodes);
switch_history(31:80) = 1 + (rand(1, 50) > 0.5);  % 中期混合
switch_history(81:end) = 2;  % 后期主要DDPG
switch_history(rand(1, episodes) < 0.1) = 3;  % 偶尔混合

% 创建修复后的Figure 1
figure('Position', [300, 300, 1000, 800], 'Name', 'GP-DDPG训练监控 - 原始代码修复版');

% 左上：训练奖励变化
subplot(2, 2, 1);
plot(episode_rewards, 'b-', 'LineWidth', 1.5);
xlabel('训练轮次');
ylabel('累积奖励');
title('训练奖励变化');
grid on;

% 右上：力误差变化
subplot(2, 2, 2);
plot(episode_force_errors, 'r-', 'LineWidth', 1.5);
xlabel('训练轮次');
ylabel('最终力误差');
title('力跟踪误差变化');
grid on;

% 左下：收敛时间变化
subplot(2, 2, 3);
plot(episode_convergence_times, 'g-', 'LineWidth', 1.5);
xlabel('训练轮次');
ylabel('收敛时间 (秒)');
title('收敛时间变化');
grid on;

% 右下：策略切换历史（使用修复后的方法）
subplot(2, 2, 4);
plot_switching_history_in_subplot(switch_history, [], 150, 0.9, 0.95);

% 保存图表
timestamp = datestr(now, 'yyyymmdd_HHMMSS');
filename = sprintf('figure1_original_code_fixed_%s.png', timestamp);
print('-dpng', '-r300', filename);

fprintf('原始代码修复版本已保存为: %s\n', filename);

% 显示修复指导
display_fix_guidance();

function display_fix_guidance()
    % 显示如何修复原始代码的指导
    
    fprintf('\n=== 原始代码修复指导 ===\n');
    
    fprintf('\n🔧 修复方法1: 修改policy_switcher.m\n');
    fprintf('在policy_switcher类中添加新方法:\n');
    fprintf('```matlab\n');
    fprintf('function plot_switching_history_in_current_subplot(obj)\n');
    fprintf('    %% 在当前子图中绘制，不创建新窗口\n');
    fprintf('    if isempty(obj.switch_history)\n');
    fprintf('        text(0.5, 0.5, ''无数据'', ''Units'', ''normalized'');\n');
    fprintf('        return;\n');
    fprintf('    end\n');
    fprintf('    plot(obj.switch_history, ''o-'', ''LineWidth'', 1.5);\n');
    fprintf('    xlabel(''时间步''); ylabel(''策略类型'');\n');
    fprintf('    title(''策略切换历史'');\n');
    fprintf('    ylim([0.5, 3.5]); yticks([1, 2, 3]);\n');
    fprintf('    yticklabels({''传统自适应'', ''GP-DDPG'', ''混合''});\n');
    fprintf('    grid on;\n');
    fprintf('end\n');
    fprintf('```\n');
    
    fprintf('\n🔧 修复方法2: 修改admittance_optimizer.m\n');
    fprintf('将第596行改为:\n');
    fprintf('```matlab\n');
    fprintf('%% 原代码: obj.policy_switcher.plot_switching_history();\n');
    fprintf('%% 修复为: obj.policy_switcher.plot_switching_history_in_current_subplot();\n');
    fprintf('```\n');
    
    fprintf('\n🔧 修复方法3: 直接在admittance_optimizer.m中实现\n');
    fprintf('将第595-596行替换为:\n');
    fprintf('```matlab\n');
    fprintf('if ~isempty(obj.policy_switcher.switch_history)\n');
    fprintf('    plot(obj.policy_switcher.switch_history, ''o-'', ''LineWidth'', 1.5);\n');
    fprintf('    xlabel(''时间步''); ylabel(''策略类型''); title(''策略切换历史'');\n');
    fprintf('    ylim([0.5, 3.5]); yticks([1, 2, 3]);\n');
    fprintf('    yticklabels({''传统自适应'', ''GP-DDPG'', ''混合''});\n');
    fprintf('    grid on;\n');
    fprintf('else\n');
    fprintf('    text(0.5, 0.5, ''无数据'', ''Units'', ''normalized'');\n');
    fprintf('end\n');
    fprintf('```\n');
    
    fprintf('\n✅ 推荐使用方法3，最简单直接！\n');
    fprintf('这样就能完全解决右下角空白的问题。\n');
end

% 运行演示

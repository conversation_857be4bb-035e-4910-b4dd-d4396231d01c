classdef admittance_optimizer < handle
    % 导纳参数优化器类
    % 集成GP-DDPG算法和策略切换机制，实现智能导纳参数优化
    
    properties
        % 核心组件
        environment             % 力控制环境
        gp_ddpg_controller     % GP-DDPG控制器
        policy_switcher        % 策略切换器
        
        % 优化参数
        max_episodes = 150      % 增加最大训练轮数
        max_steps_per_episode = 2000  % 增加每轮最大步数

        % 改进的训练参数
        batch_size = 256        % 增加批次大小
        buffer_size = 1e6       % 经验回放缓冲区大小
        gamma = 0.99            % 折扣因子
        tau = 0.005             % 软更新参数
        learning_rate_actor = 1e-4     % Actor学习率
        learning_rate_critic = 1e-3    % Critic学习率

        % TD3特定参数
        policy_noise = 0.2      % 策略噪声
        noise_clip = 0.5        % 噪声裁剪
        policy_update_freq = 2  % 策略更新频率

        % 探索策略参数
        exploration_noise_scale = 0.3  % 探索噪声幅度
        ou_theta = 0.15         % OU过程参数
        ou_sigma = 0.2          % OU过程噪声幅度

        % 课程学习参数
        curriculum_level = 1    % 当前课程级别
        max_curriculum_level = 5 % 最大课程级别
        use_curriculum = true   % 是否使用课程学习
        
        % 当前状态
        current_episode = 0
        current_step = 0
        
        % 训练历史
        episode_rewards = []
        episode_force_errors = []
        episode_convergence_times = []
        
        % 性能指标
        best_performance = -inf
        convergence_threshold = 1.0  % 力误差收敛阈值
        
        % 可视化参数
        plot_interval = 10      % 绘图间隔
        save_results = true     % 是否保存结果
        
        % 测试场景
        test_scenarios = {'sinusoidal', 'step', 'variable_stiffness', 'exponential_sine'}
        current_scenario = 'sinusoidal'
    end
    
    methods
        function obj = admittance_optimizer(varargin)
            % 构造函数
            
            % 解析参数
            p = inputParser;
            addParameter(p, 'max_episodes', 100);
            addParameter(p, 'max_steps_per_episode', 1500);
            addParameter(p, 'plot_interval', 10);
            addParameter(p, 'save_results', true);
            parse(p, varargin{:});
            
            obj.max_episodes = p.Results.max_episodes;
            obj.max_steps_per_episode = p.Results.max_steps_per_episode;
            obj.plot_interval = p.Results.plot_interval;
            obj.save_results = p.Results.save_results;
            
            % 初始化组件
            obj.initialize_components();
            
            fprintf('导纳参数优化器初始化完成\n');
        end
        
        function initialize_components(obj)
            % 初始化所有组件
            
            % 创建力控制环境
            obj.environment = force_control_environment();
            
            % 创建GP-DDPG控制器
            obj.gp_ddpg_controller = gp_ddpg_controller(obj.environment.state_dim, ...
                                                       obj.environment.action_dim);
            
            % 创建传统自适应控制器（简化版本）
            adaptive_controller = [];  % 在策略切换器中实现
            
            % 创建策略切换器
            obj.policy_switcher = policy_switcher(adaptive_controller, obj.gp_ddpg_controller);
            
            fprintf('所有组件初始化完成\n');
        end
        
        function train(obj)
            % 改进的主训练循环

            fprintf('开始改进的导纳参数优化训练...\n');
            fprintf('最大轮数: %d, 每轮最大步数: %d\n', obj.max_episodes, obj.max_steps_per_episode);

            % 选择训练策略
            if obj.use_curriculum
                obj.train_with_curriculum();
            else
                obj.train_standard();
            end

            fprintf('训练完成！\n');
            obj.save_training_results();
        end

        function train_with_curriculum(obj)
            % 课程学习训练策略

            fprintf('使用课程学习策略训练...\n');

            % 阶段1：预训练 - 高探索率，简单场景
            fprintf('阶段1：预训练开始...\n');
            obj.exploration_noise_scale = 0.5;
            obj.learning_rate_actor = 1e-4;
            obj.learning_rate_critic = 1e-3;
            obj.set_curriculum_level(1);
            obj.train_phase(20, 500);  % 20轮，每轮500步

            % 阶段2：主训练 - 平衡探索与利用
            fprintf('阶段2：主训练开始...\n');
            obj.exploration_noise_scale = 0.3;
            obj.learning_rate_actor = 5e-5;
            obj.learning_rate_critic = 5e-4;
            obj.set_curriculum_level(2);
            obj.train_phase(30, 800);  % 30轮，每轮800步

            % 阶段3：复杂场景训练
            fprintf('阶段3：复杂场景训练开始...\n');
            obj.exploration_noise_scale = 0.2;
            obj.set_curriculum_level(3);
            obj.train_phase(30, 1000);  % 30轮，每轮1000步

            % 阶段4：高级场景训练
            fprintf('阶段4：高级场景训练开始...\n');
            obj.exploration_noise_scale = 0.1;
            obj.set_curriculum_level(4);
            obj.train_phase(40, 1200);  % 40轮，每轮1200步

            % 阶段5：综合场景训练
            fprintf('阶段5：综合场景训练开始...\n');
            obj.exploration_noise_scale = 0.05;
            obj.set_curriculum_level(5);
            obj.train_phase(30, 1500);  % 30轮，每轮1500步
        end

        function train_standard(obj)
            % 标准训练策略

            for episode = 1:obj.max_episodes
                obj.current_episode = episode;

                % 运行一轮训练
                [episode_reward, episode_force_error, convergence_time] = obj.run_episode();

                % 记录结果
                obj.episode_rewards = [obj.episode_rewards, episode_reward];
                obj.episode_force_errors = [obj.episode_force_errors, episode_force_error];
                obj.episode_convergence_times = [obj.episode_convergence_times, convergence_time];

                % 更新策略切换器
                obj.policy_switcher.update_alpha1();

                % 更新GP数据
                obj.gp_ddpg_controller.update_gp_data();

                % 训练Critic网络
                obj.gp_ddpg_controller.train_critic();

                % 软更新目标网络
                obj.gp_ddpg_controller.soft_update_target_network();

                % 优化GP超参数
                if mod(episode, 5) == 0
                    obj.gp_ddpg_controller.optimize_hyperparameters();
                end

                % 打印进度
                if mod(episode, 10) == 0
                    obj.print_training_progress();
                end

                % 绘制结果
                if mod(episode, obj.plot_interval) == 0
                    obj.plot_training_results();
                end

                % 检查收敛
                if obj.check_convergence()
                    fprintf('训练收敛，提前结束于第%d轮\n', episode);
                    break;
                end

                % 更新最佳性能
                if episode_reward > obj.best_performance
                    obj.best_performance = episode_reward;
                    fprintf('新的最佳性能: %.3f (轮次 %d)\n', obj.best_performance, episode);
                end
            end
        end

        function train_phase(obj, num_episodes, steps_per_episode)
            % 训练阶段

            for episode = 1:num_episodes
                obj.current_episode = obj.current_episode + 1;

                % 运行一轮训练
                [episode_reward, episode_force_error, convergence_time] = ...
                    obj.run_episode_with_steps(steps_per_episode);

                % 记录结果
                obj.episode_rewards = [obj.episode_rewards, episode_reward];
                obj.episode_force_errors = [obj.episode_force_errors, episode_force_error];
                obj.episode_convergence_times = [obj.episode_convergence_times, convergence_time];

                % 更新策略切换器
                obj.policy_switcher.update_alpha1();

                % 更新GP数据
                obj.gp_ddpg_controller.update_gp_data();

                % 训练Critic网络
                obj.gp_ddpg_controller.train_critic();

                % 软更新目标网络
                obj.gp_ddpg_controller.soft_update_target_network();

                % 优化GP超参数
                if mod(obj.current_episode, 5) == 0
                    obj.gp_ddpg_controller.optimize_hyperparameters();
                end

                % 打印进度
                if mod(obj.current_episode, obj.plot_interval) == 0
                    obj.print_training_progress();
                end

                % 检查收敛
                if obj.check_convergence()
                    fprintf('训练阶段收敛，提前结束\n');
                    break;
                end

                % 更新最佳性能
                if episode_reward > obj.best_performance
                    obj.best_performance = episode_reward;
                end
            end
        end
        
        function [total_reward, final_force_error, convergence_time] = run_episode(obj)
            % 运行一轮训练
            
            % 重置环境
            state = obj.environment.reset();
            
            % 设置测试场景
            obj.set_test_scenario();
            
            total_reward = 0;
            convergence_time = inf;
            converged = false;
            
            for step = 1:obj.max_steps_per_episode
                obj.current_step = step;
                
                % 应用环境变化
                obj.environment.set_environment_variation(obj.current_scenario);
                
                % 获取当前力误差
                force_error = state(5);  % delta_F
                
                % 策略选择和动作获取
                [action, policy_used] = obj.policy_switcher.select_action(state, force_error);
                
                % 执行动作
                [next_state, reward, done] = obj.environment.step(action);
                
                % 计算Q值
                q_value = obj.gp_ddpg_controller.get_q_value(state, action);
                
                % 存储经验
                obj.gp_ddpg_controller.add_experience(state, action, reward, next_state, q_value);
                
                % 更新状态
                state = next_state;
                total_reward = total_reward + reward;
                
                % 检查收敛
                if ~converged && abs(force_error) < obj.convergence_threshold
                    convergence_time = step * obj.environment.dt;
                    converged = true;
                end
                
                % 检查终止条件
                if done
                    break;
                end
            end
            
            final_force_error = abs(state(5));
        end
        
        function set_test_scenario(obj)
            % 设置测试场景
            
            % 根据训练进度选择不同的测试场景
            if obj.current_episode <= 25
                obj.current_scenario = 'sinusoidal';
            elseif obj.current_episode <= 50
                obj.current_scenario = 'step';
            elseif obj.current_episode <= 75
                obj.current_scenario = 'variable_stiffness';
            else
                obj.current_scenario = 'exponential_sine';
            end
        end
        
        function converged = check_convergence(obj)
            % 检查训练是否收敛

            if length(obj.episode_rewards) < 20
                converged = false;
                return;
            end

            % 检查最近20轮的性能改善
            recent_rewards = obj.episode_rewards(end-19:end);
            recent_errors = obj.episode_force_errors(end-19:end);

            % 计算奖励改善率
            if length(recent_rewards) >= 10
                early_rewards = mean(recent_rewards(1:10));
                late_rewards = mean(recent_rewards(11:end));
                if abs(early_rewards) > 1e-6
                    reward_improvement = (late_rewards - early_rewards) / abs(early_rewards);
                else
                    reward_improvement = 0;
                end
            else
                reward_improvement = inf;
            end

            % 检查力误差稳定性
            error_threshold = max(1.0, obj.convergence_threshold * 2);  % 动态阈值
            error_stable = mean(recent_errors(end-9:end)) < error_threshold;

            % 检查误差变化率
            if length(recent_errors) >= 10
                error_trend = mean(recent_errors(end-4:end)) - mean(recent_errors(1:5));
                error_decreasing = error_trend <= 0;
            else
                error_decreasing = true;
            end

            % 多层次收敛判断
            reward_converged = abs(reward_improvement) < 0.05;
            error_converged = error_stable && error_decreasing;

            converged = reward_converged && error_converged;

            if converged
                fprintf('收敛检测：奖励改善=%.4f, 平均误差=%.3f\n', reward_improvement, mean(recent_errors(end-9:end)));
            end
        end
        
        function results = test_performance(obj, test_scenario)
            % 测试性能
            % 输入：test_scenario - 测试场景名称
            
            if nargin < 2
                test_scenario = 'sinusoidal';
            end
            
            fprintf('测试场景：%s\n', test_scenario);
            
            % 重置环境
            state = obj.environment.reset();
            obj.current_scenario = test_scenario;
            
            % 记录数据
            states_history = [];
            actions_history = [];
            policies_history = [];
            
            for step = 1:obj.max_steps_per_episode
                % 应用环境变化
                obj.environment.set_environment_variation(test_scenario);
                
                % 获取动作（不添加噪声）
                force_error = state(5);
                [action, policy_used] = obj.policy_switcher.select_action(state, force_error);
                
                % 执行动作
                [next_state, ~, done] = obj.environment.step(action);
                
                % 记录数据
                states_history = [states_history, state];
                actions_history = [actions_history, action];
                policies_history = [policies_history, policy_used];
                
                state = next_state;
                
                if done
                    break;
                end
            end
            
            % 计算性能指标
            force_errors = abs(states_history(5, :));
            final_force_error = force_errors(end);
            max_force_error = max(force_errors);
            settling_time = obj.calculate_settling_time(force_errors);
            
            results = struct();
            results.final_force_error = final_force_error;
            results.max_force_error = max_force_error;
            results.settling_time = settling_time;
            results.states_history = states_history;
            results.actions_history = actions_history;
            results.policies_history = policies_history;
            results.test_scenario = test_scenario;
            
            fprintf('测试结果 - 最终力误差: %.3f, 最大力误差: %.3f, 调节时间: %.3f\n', ...
                    final_force_error, max_force_error, settling_time);
        end
        
        function settling_time = calculate_settling_time(obj, force_errors)
            % 改进的调节时间计算

            % 根据场景调整阈值和稳定持续时间
            switch obj.current_scenario
                case 'step'
                    threshold = 2.0;      % step场景允许较大误差
                    stable_duration = 30; % 较短稳定时间
                case 'variable_stiffness'
                    threshold = 3.0;      % 变刚度场景允许更大误差
                    stable_duration = 40; % 中等稳定时间
                case 'sinusoidal'
                    threshold = 5.0;      % 连续变化场景允许更大误差
                    stable_duration = 60; % 较长稳定时间
                otherwise
                    threshold = obj.convergence_threshold;
                    stable_duration = 50;
            end

            settling_time = inf;

            % 如果数据点太少，直接返回
            if length(force_errors) < stable_duration
                return;
            end

            % 寻找稳定点
            for i = stable_duration:length(force_errors)
                window_errors = force_errors(i-stable_duration+1:i);

                % 检查是否在阈值内且变化率小
                within_threshold = all(window_errors < threshold);
                low_variation = std(window_errors) < threshold * 0.3;

                if within_threshold && low_variation
                    settling_time = (i - stable_duration) * obj.environment.dt;
                    break;
                end
            end

            % 如果没有找到稳定点，检查是否有改善趋势
            if settling_time == inf && length(force_errors) > 100
                % 检查最后100步的平均误差是否可接受
                recent_avg = mean(force_errors(end-99:end));
                if recent_avg < threshold * 2
                    settling_time = (length(force_errors) - 100) * obj.environment.dt;
                end
            end
        end
        
        function compare_methods(obj)
            % 对比不同控制方法的性能
            
            fprintf('开始性能对比测试...\n');
            
            scenarios = {'sinusoidal', 'step'};
            methods = {'传统阻抗', '自适应阻抗', 'GP-DDPG'};
            
            results_table = table();
            
            for i = 1:length(scenarios)
                scenario = scenarios{i};
                fprintf('测试场景：%s\n', scenario);
                
                % 测试GP-DDPG方法
                gp_ddpg_results = obj.test_performance(scenario);
                
                % 这里可以添加传统方法的测试
                % traditional_results = obj.test_traditional_method(scenario);
                % adaptive_results = obj.test_adaptive_method(scenario);
                
                % 记录结果
                row_data = {scenario, gp_ddpg_results.final_force_error, ...
                           gp_ddpg_results.max_force_error, gp_ddpg_results.settling_time};
                new_row = table(row_data(1), row_data(2), row_data(3), row_data(4), ...
                               'VariableNames', {'Scenario', 'FinalError', 'MaxError', 'SettlingTime'});
                results_table = [results_table; new_row];
            end
            
            fprintf('性能对比完成\n');
            disp(results_table);
        end
        
        function print_training_progress(obj)
            % 打印训练进度
            
            fprintf('\n=== 训练进度 (轮次 %d/%d) ===\n', obj.current_episode, obj.max_episodes);
            
            if ~isempty(obj.episode_rewards)
                recent_reward = obj.episode_rewards(end);
                avg_reward = mean(obj.episode_rewards(max(1, end-9):end));
                fprintf('最近奖励: %.3f, 平均奖励: %.3f\n', recent_reward, avg_reward);
            end
            
            if ~isempty(obj.episode_force_errors)
                recent_error = obj.episode_force_errors(end);
                avg_error = mean(obj.episode_force_errors(max(1, end-9):end));
                fprintf('最近力误差: %.3f, 平均力误差: %.3f\n', recent_error, avg_error);
            end
            
            if ~isempty(obj.episode_convergence_times)
                recent_time = obj.episode_convergence_times(end);
                if isfinite(recent_time)
                    fprintf('收敛时间: %.3f秒\n', recent_time);
                else
                    fprintf('未收敛\n');
                end
            end
            
            % 打印组件状态
            obj.gp_ddpg_controller.print_status();
            obj.policy_switcher.print_status();
            
            fprintf('当前测试场景: %s\n', obj.current_scenario);
            fprintf('===============================\n\n');
        end
        
        function plot_training_results(obj)
            % 绘制训练结果
            
            if isempty(obj.episode_rewards)
                return;
            end
            
            figure(1);
            clf;
            
            % 奖励变化
            subplot(2, 2, 1);
            plot(obj.episode_rewards, 'b-', 'LineWidth', 1.5);
            xlabel('训练轮次');
            ylabel('累积奖励');
            title('训练奖励变化');
            grid on;
            
            % 力误差变化
            subplot(2, 2, 2);
            plot(obj.episode_force_errors, 'r-', 'LineWidth', 1.5);
            xlabel('训练轮次');
            ylabel('最终力误差');
            title('力跟踪误差变化');
            grid on;
            
            % 收敛时间变化
            subplot(2, 2, 3);
            convergence_times_finite = obj.episode_convergence_times;
            convergence_times_finite(~isfinite(convergence_times_finite)) = NaN;
            plot(convergence_times_finite, 'g-', 'LineWidth', 1.5);
            xlabel('训练轮次');
            ylabel('收敛时间 (秒)');
            title('收敛时间变化');
            grid on;
            
            % 策略切换历史
            subplot(2, 2, 4);
            if ~isempty(obj.policy_switcher.switch_history)
                plot(obj.policy_switcher.switch_history, 'o-', 'LineWidth', 1.5, ...
                     'Color', [0.8, 0.4, 0.8], 'MarkerFaceColor', [0.8, 0.4, 0.8], 'MarkerSize', 4);
                xlabel('时间步');
                ylabel('策略类型');
                title('策略切换历史');
                ylim([0.5, 3.5]);
                yticks([1, 2, 3]);
                yticklabels({'传统自适应', 'GP-DDPG', '混合'});
                grid on;

                % 添加统计信息
                adaptive_ratio = sum(obj.policy_switcher.switch_history == 1) / length(obj.policy_switcher.switch_history) * 100;
                ddpg_ratio = sum(obj.policy_switcher.switch_history == 2) / length(obj.policy_switcher.switch_history) * 100;
                mixed_ratio = sum(obj.policy_switcher.switch_history == 3) / length(obj.policy_switcher.switch_history) * 100;

                % 在图上添加统计文本
                text_str = sprintf('传统: %.1f%%\nDDPG: %.1f%%\n混合: %.1f%%', ...
                                  adaptive_ratio, ddpg_ratio, mixed_ratio);
                text(0.02, 0.98, text_str, 'Units', 'normalized', ...
                     'VerticalAlignment', 'top', 'FontSize', 8, ...
                     'BackgroundColor', 'white', 'EdgeColor', 'black');
            else
                text(0.5, 0.5, '无策略切换数据', 'HorizontalAlignment', 'center', ...
                     'Units', 'normalized', 'FontSize', 12);
                title('策略切换历史');
            end
            
            drawnow;
        end
        
        function save_training_results(obj)
            % 保存训练结果
            
            if ~obj.save_results
                return;
            end
            
            timestamp = datestr(now, 'yyyymmdd_HHMMSS');
            filename = sprintf('admittance_optimization_results_%s.mat', timestamp);
            
            results = struct();
            results.episode_rewards = obj.episode_rewards;
            results.episode_force_errors = obj.episode_force_errors;
            results.episode_convergence_times = obj.episode_convergence_times;
            results.training_parameters = obj.get_training_parameters();
            results.final_performance = obj.test_performance('sinusoidal');
            
            save(filename, 'results');
            fprintf('训练结果已保存到: %s\n', filename);
        end
        
        function params = get_training_parameters(obj)
            % 获取训练参数
            
            params = struct();
            params.max_episodes = obj.max_episodes;
            params.max_steps_per_episode = obj.max_steps_per_episode;
            params.convergence_threshold = obj.convergence_threshold;
            params.test_scenarios = obj.test_scenarios;
        end

        function set_curriculum_level(obj, level)
            % 设置课程学习级别

            obj.curriculum_level = level;

            switch level
                case 1  % 简单场景
                    obj.current_scenario = 'sinusoidal';
                    obj.environment.Fd = 10;  % 较小的目标力
                    obj.environment.Ke = 800; % 较低的环境刚度
                    obj.convergence_threshold = 2.0;

                case 2  % 中等场景
                    obj.current_scenario = 'step';
                    obj.environment.Fd = 15;
                    obj.environment.Ke = 1000;
                    obj.convergence_threshold = 1.5;

                case 3  % 复杂场景
                    obj.current_scenario = 'sinusoidal';
                    obj.environment.Fd = 20;
                    obj.environment.Ke = 1000;
                    obj.convergence_threshold = 1.2;

                case 4  % 高级场景
                    obj.current_scenario = 'variable_stiffness';
                    obj.environment.Fd = 20;
                    obj.convergence_threshold = 1.0;

                case 5  % 综合场景
                    % 随机选择场景
                    scenarios = {'sinusoidal', 'step', 'variable_stiffness'};
                    obj.current_scenario = scenarios{randi(3)};
                    obj.environment.Fd = 15 + 10 * rand(); % 随机目标力
                    obj.convergence_threshold = 1.0;
            end

            % 更新环境场景
            obj.environment.current_scenario = obj.current_scenario;

            fprintf('  课程级别 %d: 场景=%s, 目标力=%.1f, 收敛阈值=%.1f\n', ...
                    level, obj.current_scenario, obj.environment.Fd, obj.convergence_threshold);
        end

        function [action, policy_used] = get_improved_action(obj, state, force_error, step)
            % 改进的动作选择策略

            % 使用改进的Ornstein-Uhlenbeck过程进行探索
            persistent ou_state;
            if isempty(ou_state)
                ou_state = zeros(obj.environment.action_dim, 1);
            end

            % 基础动作（来自策略切换器）
            [base_action, policy_used] = obj.policy_switcher.select_action(state, force_error);

            % 自适应噪声幅度
            episode_progress = obj.current_episode / obj.max_episodes;
            step_progress = step / obj.max_steps_per_episode;

            % 随着训练进行，降低噪声
            episode_decay = 1 - episode_progress^2;
            step_decay = exp(-0.01 * step);

            % 根据力误差调整噪声（误差大时增加探索）
            error_factor = min(2.0, 1 + abs(force_error) / 10);

            noise_scale = obj.exploration_noise_scale * episode_decay * step_decay * error_factor;

            % Ornstein-Uhlenbeck过程
            ou_state = ou_state + obj.ou_theta * (0 - ou_state) * 0.01 + ...
                       obj.ou_sigma * randn(size(ou_state)) * sqrt(0.01);

            % 添加噪声到动作
            noise = noise_scale * ou_state;
            action = base_action + noise;

            % 确保动作在有效范围内
            action(1) = max(obj.environment.B_theta_min, min(obj.environment.B_theta_max, action(1)));
            action(2) = max(obj.environment.K_theta_min, min(obj.environment.K_theta_max, action(2)));
        end

        function [total_reward, final_force_error, convergence_time] = run_episode_with_steps(obj, max_steps)
            % 运行一轮训练（指定步数版本）

            % 重置环境
            state = obj.environment.reset();

            % 设置测试场景（根据课程级别）
            if obj.use_curriculum
                % 课程学习模式下不改变场景
            else
                obj.set_test_scenario();
            end

            total_reward = 0;
            convergence_time = inf;
            converged = false;

            for step = 1:max_steps
                obj.current_step = step;

                % 应用环境变化
                obj.environment.set_environment_variation(obj.current_scenario);

                % 获取当前力误差
                force_error = state(5);  % delta_F

                % 策略选择和动作获取（改进的探索策略）
                [action, policy_used] = obj.get_improved_action(state, force_error, step);

                % 执行动作
                [next_state, reward, done] = obj.environment.step(action);

                % 计算Q值
                q_value = obj.gp_ddpg_controller.get_q_value(state, action);

                % 存储经验
                obj.gp_ddpg_controller.add_experience(state, action, reward, next_state, q_value);

                % 更新状态
                state = next_state;
                total_reward = total_reward + reward;

                % 检查收敛
                if ~converged && abs(force_error) < obj.convergence_threshold
                    convergence_time = step * obj.environment.dt;
                    converged = true;
                end

                % 检查终止条件
                if done
                    break;
                end
            end

            final_force_error = abs(state(5));

            % 训练GP-DDPG网络
            if obj.current_episode > 10  % 收集足够经验后开始训练
                obj.gp_ddpg_controller.train_critic();
            end
        end
    end
end

# GP-DDPG自适应导纳控制系统

基于强化学习的机器人导纳参数优化方法实现，复现论文《An Admittance Parameter Optimization Method Based on Reinforcement Learning for Robot Force Control》中的GP-DDPG算法。

## 项目概述

本项目在现有的自适应阻抗控制系统基础上，集成了GP-DDPG（Gaussian Process Deep Deterministic Policy Gradient）强化学习算法，实现了智能导纳参数优化和策略切换机制。

### 主要特性

- **GP-DDPG算法实现**: 结合高斯过程和DDPG的智能控制算法
- **策略切换机制**: 传统自适应控制与DDPG控制的智能切换
- **导纳参数优化**: 实时优化机器人导纳参数（刚度、阻尼）
- **多场景验证**: 支持环境位置变化、期望力突变等测试场景
- **性能对比分析**: 全面的控制方法性能对比和可视化

## 文件结构

```
ping_01/
├── README.md                           # 项目说明文档
├── simulink_integration_guide.md       # Simulink集成指导
├── 
├── # 原有文件
├── adaptive_admittance_plot.m          # 原始绘图脚本
├── duan_adaptive_admittance.slx        # 原始Simulink模型
├── 自适应变阻抗控制.rar               # 原始资料包
├── 
├── # 核心算法实现
├── force_control_environment.m         # 力控制环境类
├── gp_ddpg_controller.m               # GP-DDPG控制器类
├── policy_switcher.m                  # 策略切换器类
├── admittance_optimizer.m             # 导纳参数优化器类
├── 
├── # 可视化和分析
├── admittance_ddpg_plot.m             # 扩展绘图脚本
├── run_ddpg_admittance_control.m      # 主运行脚本
└── 
```

## 快速开始

### 环境要求

- MATLAB R2020b或更高版本
- Simulink
- Deep Learning Toolbox（推荐）
- Control System Toolbox（推荐）

### 运行步骤

1. **克隆或下载项目文件**
   ```matlab
   % 确保所有.m文件在MATLAB路径中
   addpath(pwd);
   ```

2. **运行完整实验**
   ```matlab
   % 运行主实验脚本
   run_ddpg_admittance_control();
   ```

3. **单独测试组件**
   ```matlab
   % 测试GP-DDPG控制器
   optimizer = admittance_optimizer('max_episodes', 20);
   optimizer.train();
   
   % 测试性能对比
   optimizer.compare_methods();
   ```

4. **可视化结果**
   ```matlab
   % 如果有Simulink仿真输出数据
   admittance_ddpg_plot(out, 'save_plots', true);
   ```

## 核心组件说明

### 1. 力控制环境 (force_control_environment.m)

实现机器人力控制的仿真环境，包括：
- 状态空间：位置、速度、接触力、期望力、力误差、位置误差
- 动作空间：导纳参数增量（阻尼、刚度）
- 奖励函数：基于力跟踪误差和能耗的复合奖励
- 环境变化：支持多种测试场景

### 2. GP-DDPG控制器 (gp_ddpg_controller.m)

实现论文中的GP-DDPG算法：
- **Actor网络**: 使用高斯过程模型
- **Critic网络**: 深度神经网络
- **经验回放**: 精英选择策略
- **超参数优化**: 最大似然估计

### 3. 策略切换器 (policy_switcher.m)

实现智能策略切换机制：
- **切换条件**: 基于力误差和系统稳定性
- **平滑过渡**: 避免控制突变
- **性能监控**: 实时评估控制效果

### 4. 导纳参数优化器 (admittance_optimizer.m)

集成所有组件的主控制器：
- **训练管理**: 控制整个训练过程
- **性能评估**: 多场景测试和对比
- **结果保存**: 自动保存训练结果

## 算法原理

### GP-DDPG算法

基于论文实现的改进DDPG算法：

1. **高斯过程Actor**: 
   ```
   π_θ(s) = GP(s | θ_s, θ_k)
   θ_s = {X, Y} (精英样本)
   θ_k = {σ_f, L, σ_n} (核函数超参数)
   ```

2. **策略选择**:
   ```
   π = {π_I,  if α₁ ≥ ε or ΔF ∈ U(0,δ)
       {π_θ,  otherwise
   ```

3. **奖励函数**:
   ```
   R = -(β·ΔF² + (1-β)·Ẋ²) - ξ·∑(aᵢ²/aᵢ_max²)
   ```

### 性能优势

相比传统方法的改进：
- **收敛速度**: 提升33%以上
- **调节时间**: 缩短44%
- **鲁棒性**: 更好的环境适应能力
- **智能化**: 自动参数优化

## 实验结果

### 测试场景

1. **环境位置正弦变化**: 测试跟踪性能
2. **期望接触力突变**: 测试响应速度
3. **变刚度环境**: 测试适应能力
4. **复杂环境**: 测试鲁棒性

### 性能指标

- **力跟踪精度**: RMSE, 最大误差
- **收敛性能**: 调节时间, 超调量
- **稳定性**: 稳态误差, 振荡幅度
- **鲁棒性**: 不同环境下的性能一致性

## Simulink集成

详细的Simulink模型修改指导请参考 `simulink_integration_guide.md`。

主要修改包括：
1. 添加GP-DDPG控制器MATLAB Function块
2. 修改导纳模型支持可变参数
3. 扩展数据输出结构
4. 集成策略切换逻辑

## 参数配置

### 训练参数
```matlab
max_episodes = 100;           % 最大训练轮数
max_steps_per_episode = 1500; % 每轮最大步数
learning_rate = 1e-3;         % 学习率
batch_size = 64;              % 批次大小
```

### 环境参数
```matlab
Ke = 1000;                    % 环境刚度 (N/m)
Fd = 20;                      % 期望接触力 (N)
dt = 0.01;                    % 时间步长 (s)
```

### GP参数
```matlab
sigma_f = 1.0;                % 信号方差
sigma_n = 0.1;                % 噪声方差
Ne = 50;                      % 精英样本数量
```

## 故障排除

### 常见问题

1. **内存不足**: 减少缓冲区大小或训练轮数
2. **收敛慢**: 调整学习率和奖励函数权重
3. **数值不稳定**: 检查参数范围限制
4. **Simulink错误**: 确保所有依赖文件在路径中

### 调试建议

- 使用`verbose`模式查看详细输出
- 检查各组件的`print_status()`方法
- 逐步测试各个模块
- 保存中间结果进行分析

## 扩展功能

### 可能的改进方向

1. **多自由度扩展**: 支持6DOF机器人
2. **在线学习**: 实时参数更新
3. **安全机制**: 故障检测和安全切换
4. **硬件部署**: 实际机器人验证

## 参考文献

1. Hu, X., et al. "An Admittance Parameter Optimization Method Based on Reinforcement Learning for Robot Force Control." Actuators 2024, 13, 354.

2. Lillicrap, T. P., et al. "Continuous control with deep reinforcement learning." arXiv preprint arXiv:1509.02971 (2015).

3. Hogan, N. "Impedance control: An approach to manipulation." 1984 American control conference. IEEE, 1984.

## 许可证

本项目仅用于学术研究和教育目的。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 邮件联系

---

**注意**: 本项目基于学术论文实现，建议在充分理解算法原理后使用。实际应用时请进行充分的安全测试。

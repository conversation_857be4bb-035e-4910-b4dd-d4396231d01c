function test_improved_gp_ddpg()
    % 测试改进的GP-DDPG算法性能
    % 对比原始版本和改进版本的性能差异
    
    fprintf('=== 改进的GP-DDPG算法性能测试 ===\n\n');
    
    % 测试参数
    test_episodes = 80;
    test_steps = 1000;
    
    % 测试场景
    scenarios = {'sinusoidal', 'step', 'variable_stiffness'};
    
    % 存储结果
    baseline_results = struct();
    improved_results = struct();
    
    %% 测试基准方法（原始实现）
    fprintf('1. 测试基准方法（原始实现）...\n');
    
    baseline_optimizer = admittance_optimizer('max_episodes', test_episodes, ...
                                             'max_steps_per_episode', test_steps, ...
                                             'use_curriculum', false);
    
    % 训练基准方法
    fprintf('   训练基准优化器...\n');
    tic;
    baseline_optimizer.train();
    baseline_training_time = toc;
    
    % 测试基准方法在各场景下的性能
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        fprintf('   测试场景: %s\n', scenario);
        
        baseline_results.(scenario) = baseline_optimizer.test_performance(scenario);
        baseline_results.(scenario).training_time = baseline_training_time;
        
        fprintf('     力误差: %.3f N, 调节时间: %.3f s\n', ...
                baseline_results.(scenario).final_force_error, ...
                baseline_results.(scenario).settling_time);
    end
    
    %% 测试改进方法
    fprintf('\n2. 测试改进方法...\n');
    
    improved_optimizer = admittance_optimizer('max_episodes', test_episodes, ...
                                             'max_steps_per_episode', test_steps, ...
                                             'use_curriculum', true, ...
                                             'exploration_noise_scale', 0.3, ...
                                             'learning_rate_actor', 1e-4, ...
                                             'learning_rate_critic', 1e-3);
    
    % 训练改进方法
    fprintf('   训练改进优化器（使用课程学习）...\n');
    tic;
    improved_optimizer.train();
    improved_training_time = toc;
    
    % 测试改进方法在各场景下的性能
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        fprintf('   测试场景: %s\n', scenario);
        
        improved_results.(scenario) = improved_optimizer.test_performance(scenario);
        improved_results.(scenario).training_time = improved_training_time;
        
        fprintf('     力误差: %.3f N, 调节时间: %.3f s\n', ...
                improved_results.(scenario).final_force_error, ...
                improved_results.(scenario).settling_time);
    end
    
    %% 性能对比分析
    fprintf('\n3. 性能对比分析\n');
    fprintf('=' * ones(1, 50));
    fprintf('\n');
    
    % 计算改善百分比
    total_force_improvement = 0;
    total_time_improvement = 0;
    valid_scenarios = 0;
    
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        
        % 力误差改善
        force_improvement = (baseline_results.(scenario).final_force_error - ...
                           improved_results.(scenario).final_force_error) / ...
                           baseline_results.(scenario).final_force_error * 100;
        
        % 调节时间改善
        time_improvement = (baseline_results.(scenario).settling_time - ...
                          improved_results.(scenario).settling_time) / ...
                          baseline_results.(scenario).settling_time * 100;
        
        fprintf('场景: %s\n', scenario);
        fprintf('  基准方法: 力误差=%.3f N, 调节时间=%.3f s\n', ...
                baseline_results.(scenario).final_force_error, ...
                baseline_results.(scenario).settling_time);
        fprintf('  改进方法: 力误差=%.3f N, 调节时间=%.3f s\n', ...
                improved_results.(scenario).final_force_error, ...
                improved_results.(scenario).settling_time);
        fprintf('  改善: 力误差%.1f%%, 调节时间%.1f%%\n\n', ...
                force_improvement, time_improvement);
        
        if isfinite(force_improvement) && isfinite(time_improvement)
            total_force_improvement = total_force_improvement + force_improvement;
            total_time_improvement = total_time_improvement + time_improvement;
            valid_scenarios = valid_scenarios + 1;
        end
    end
    
    % 总体改善
    if valid_scenarios > 0
        avg_force_improvement = total_force_improvement / valid_scenarios;
        avg_time_improvement = total_time_improvement / valid_scenarios;
        
        fprintf('总体性能改善:\n');
        fprintf('  平均力误差改善: %.1f%%\n', avg_force_improvement);
        fprintf('  平均调节时间改善: %.1f%%\n', avg_time_improvement);
        fprintf('  训练时间对比: 基准%.1fs vs 改进%.1fs\n', ...
                baseline_training_time, improved_training_time);
    end
    
    %% 绘制对比结果
    plot_comparison_results(baseline_results, improved_results, scenarios);
    
    %% 保存结果
    save_comparison_results(baseline_results, improved_results, scenarios);
    
    fprintf('\n测试完成！\n');
end

function plot_comparison_results(baseline_results, improved_results, scenarios)
    % 绘制对比结果
    
    figure('Position', [100, 100, 1200, 800]);
    
    % 力误差对比
    subplot(2, 3, 1);
    force_errors_baseline = zeros(1, length(scenarios));
    force_errors_improved = zeros(1, length(scenarios));
    
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        force_errors_baseline(i) = baseline_results.(scenario).final_force_error;
        force_errors_improved(i) = improved_results.(scenario).final_force_error;
    end
    
    x = 1:length(scenarios);
    bar(x-0.2, force_errors_baseline, 0.4, 'DisplayName', '基准方法');
    hold on;
    bar(x+0.2, force_errors_improved, 0.4, 'DisplayName', '改进方法');
    
    set(gca, 'XTick', x, 'XTickLabel', scenarios);
    ylabel('最终力误差 (N)');
    title('力跟踪精度对比');
    legend('Location', 'best');
    grid on;
    
    % 调节时间对比
    subplot(2, 3, 2);
    settling_times_baseline = zeros(1, length(scenarios));
    settling_times_improved = zeros(1, length(scenarios));
    
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        settling_times_baseline(i) = baseline_results.(scenario).settling_time;
        settling_times_improved(i) = improved_results.(scenario).settling_time;
    end
    
    bar(x-0.2, settling_times_baseline, 0.4, 'DisplayName', '基准方法');
    hold on;
    bar(x+0.2, settling_times_improved, 0.4, 'DisplayName', '改进方法');
    
    set(gca, 'XTick', x, 'XTickLabel', scenarios);
    ylabel('调节时间 (s)');
    title('收敛速度对比');
    legend('Location', 'best');
    grid on;
    
    % 性能改善百分比
    subplot(2, 3, 3);
    force_improvements = zeros(1, length(scenarios));
    time_improvements = zeros(1, length(scenarios));
    
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        force_improvements(i) = (force_errors_baseline(i) - force_errors_improved(i)) / force_errors_baseline(i) * 100;
        time_improvements(i) = (settling_times_baseline(i) - settling_times_improved(i)) / settling_times_baseline(i) * 100;
    end
    
    bar(x-0.2, force_improvements, 0.4, 'DisplayName', '力误差改善');
    hold on;
    bar(x+0.2, time_improvements, 0.4, 'DisplayName', '调节时间改善');
    
    set(gca, 'XTick', x, 'XTickLabel', scenarios);
    ylabel('性能改善 (%)');
    title('改进方法性能提升');
    legend('Location', 'best');
    grid on;
    
    % 训练曲线对比（如果有数据）
    subplot(2, 3, 4);
    % 这里可以添加训练曲线对比
    text(0.5, 0.5, '训练曲线对比', 'HorizontalAlignment', 'center', ...
         'VerticalAlignment', 'middle', 'FontSize', 14);
    
    % 鲁棒性分析
    subplot(2, 3, 5);
    % 这里可以添加鲁棒性分析
    text(0.5, 0.5, '鲁棒性分析', 'HorizontalAlignment', 'center', ...
         'VerticalAlignment', 'middle', 'FontSize', 14);
    
    % 综合评分
    subplot(2, 3, 6);
    overall_scores_baseline = (1 ./ force_errors_baseline) .* (1 ./ settling_times_baseline);
    overall_scores_improved = (1 ./ force_errors_improved) .* (1 ./ settling_times_improved);
    
    bar(x-0.2, overall_scores_baseline, 0.4, 'DisplayName', '基准方法');
    hold on;
    bar(x+0.2, overall_scores_improved, 0.4, 'DisplayName', '改进方法');
    
    set(gca, 'XTick', x, 'XTickLabel', scenarios);
    ylabel('综合评分');
    title('综合性能对比');
    legend('Location', 'best');
    grid on;
    
    sgtitle('改进的GP-DDPG算法性能对比分析', 'FontSize', 16, 'FontWeight', 'bold');
    
    % 保存图形
    timestamp = datestr(now, 'yyyymmdd_HHMMSS');
    filename = sprintf('improved_gp_ddpg_comparison_%s.png', timestamp);
    print('-dpng', '-r300', filename);
    fprintf('对比结果图已保存: %s\n', filename);
end

function save_comparison_results(baseline_results, improved_results, scenarios)
    % 保存对比结果
    
    timestamp = datestr(now, 'yyyymmdd_HHMMSS');
    filename = sprintf('improved_gp_ddpg_results_%s.mat', timestamp);
    
    results = struct();
    results.baseline_results = baseline_results;
    results.improved_results = improved_results;
    results.scenarios = scenarios;
    results.test_time = timestamp;
    
    save(filename, 'results');
    fprintf('对比结果已保存: %s\n', filename);
end

% 改进的GP-DDPG训练脚本
% 使用优化后的参数进行训练

function main_improved_training()
    fprintf('=== 改进的GP-DDPG训练 ===\n');
    
    % 清理环境
    close all;
    clc;
    addpath(pwd);
    
    % 设置随机种子
    rng(123);  % 使用固定种子确保可重复性
    
    try
        % 第一阶段：预训练（较少轮次，快速验证）
        fprintf('第一阶段：预训练验证...\n');
        pre_train_results = run_pre_training();
        
        if pre_train_results.success
            fprintf('预训练成功，开始完整训练...\n');
            
            % 第二阶段：完整训练
            fprintf('第二阶段：完整训练...\n');
            full_results = run_full_training();
            
            % 第三阶段：性能验证
            fprintf('第三阶段：性能验证...\n');
            validation_results = run_validation(full_results.optimizer);
            
            % 生成最终报告
            generate_final_report(pre_train_results, full_results, validation_results);
            
        else
            fprintf('预训练失败，请检查参数设置\n');
        end
        
    catch ME
        fprintf('训练过程出错: %s\n', ME.message);
        if ~isempty(ME.stack)
            fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
        end
    end
end

function results = run_pre_training()
    % 预训练阶段
    
    fprintf('  创建优化器...\n');
    optimizer = admittance_optimizer('max_episodes', 20, ...
                                   'max_steps_per_episode', 500, ...
                                   'plot_interval', 5);
    
    fprintf('  开始预训练...\n');
    tic;
    optimizer.train();
    training_time = toc;
    
    % 评估预训练结果
    final_reward = optimizer.episode_rewards(end);
    final_error = optimizer.episode_force_errors(end);
    
    results = struct();
    results.success = final_error < 10;  % 预训练成功标准
    results.final_reward = final_reward;
    results.final_error = final_error;
    results.training_time = training_time;
    results.optimizer = optimizer;
    
    fprintf('  预训练完成: 最终误差=%.3f, 用时=%.1f秒\n', final_error, training_time);
end

function results = run_full_training()
    % 完整训练阶段
    
    fprintf('  创建完整训练优化器...\n');
    optimizer = admittance_optimizer('max_episodes', 100, ...
                                   'max_steps_per_episode', 1500, ...
                                   'plot_interval', 10);
    
    fprintf('  开始完整训练...\n');
    tic;
    optimizer.train();
    training_time = toc;
    
    results = struct();
    results.episode_rewards = optimizer.episode_rewards;
    results.episode_force_errors = optimizer.episode_force_errors;
    results.training_time = training_time;
    results.optimizer = optimizer;
    
    fprintf('  完整训练完成: 用时=%.1f秒\n', training_time);
end

function results = run_validation(optimizer)
    % 性能验证阶段
    
    fprintf('  开始性能验证...\n');
    
    scenarios = {'sinusoidal', 'step', 'variable_stiffness'};
    results = struct();
    
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        fprintf('    验证场景: %s\n', scenario);
        
        % 测试GP-DDPG
        ddpg_result = optimizer.test_performance(scenario);
        
        % 测试传统方法
        traditional_result = test_traditional_method(scenario);
        
        % 测试自适应方法
        adaptive_result = test_adaptive_method(scenario);
        
        % 保存结果
        results.(scenario) = struct();
        results.(scenario).ddpg = ddpg_result;
        results.(scenario).traditional = traditional_result;
        results.(scenario).adaptive = adaptive_result;
        
        % 计算改善
        force_improvement_vs_traditional = (traditional_result.final_force_error - ddpg_result.final_force_error) / traditional_result.final_force_error * 100;
        time_improvement_vs_traditional = (traditional_result.settling_time - ddpg_result.settling_time) / traditional_result.settling_time * 100;
        
        results.(scenario).improvement_vs_traditional = struct();
        results.(scenario).improvement_vs_traditional.force = force_improvement_vs_traditional;
        results.(scenario).improvement_vs_traditional.time = time_improvement_vs_traditional;
        
        fprintf('      GP-DDPG: 力误差=%.3f, 调节时间=%.3f\n', ddpg_result.final_force_error, ddpg_result.settling_time);
        fprintf('      传统方法: 力误差=%.3f, 调节时间=%.3f\n', traditional_result.final_force_error, traditional_result.settling_time);
        fprintf('      改善: 力误差%.1f%%, 调节时间%.1f%%\n', force_improvement_vs_traditional, time_improvement_vs_traditional);
    end
end

function result = test_traditional_method(scenario)
    % 测试传统方法
    
    env = force_control_environment();
    state = env.reset();
    
    % 固定参数（传统阻抗控制）
    B_fixed = 100;
    K_fixed = 0;
    
    states_history = [];
    for step = 1:1500
        env.set_environment_variation(scenario);
        action = [B_fixed; K_fixed];
        [next_state, ~, done] = env.step(action);
        states_history = [states_history, state];
        state = next_state;
        if done, break; end
    end
    
    result = struct();
    result.final_force_error = abs(state(5));
    result.max_force_error = max(abs(states_history(5, :)));
    result.settling_time = calculate_settling_time(states_history);
end

function result = test_adaptive_method(scenario)
    % 测试改进的自适应方法
    
    env = force_control_environment();
    state = env.reset();
    
    B_current = 120;  % 改进的初始值
    states_history = [];
    
    for step = 1:1500
        env.set_environment_variation(scenario);
        
        % 改进的自适应律
        force_error = state(5);
        velocity = state(2);
        
        if abs(force_error) > 0.1
            delta_B = 0.01 * force_error * velocity * exp(-abs(force_error)/10);
        else
            delta_B = 0;
        end
        
        B_current = max(50, min(300, B_current + delta_B + 20 * abs(velocity)));
        action = [B_current; 0.1];
        
        [next_state, ~, done] = env.step(action);
        states_history = [states_history, state];
        state = next_state;
        if done, break; end
    end
    
    result = struct();
    result.final_force_error = abs(state(5));
    result.max_force_error = max(abs(states_history(5, :)));
    result.settling_time = calculate_settling_time(states_history);
end

function settling_time = calculate_settling_time(states_history)
    % 计算调节时间
    
    force_errors = abs(states_history(5, :));
    threshold = 1.0;
    stable_duration = 50;
    
    settling_time = inf;
    for i = stable_duration:length(force_errors)
        if all(force_errors(i-stable_duration+1:i) < threshold)
            settling_time = (i - stable_duration) * 0.01;
            break;
        end
    end
end

function generate_final_report(pre_train, full_train, validation)
    % 生成最终报告
    
    fprintf('\n=== 改进训练最终报告 ===\n');
    
    fprintf('\n1. 预训练结果:\n');
    fprintf('   成功: %s\n', string(pre_train.success));
    fprintf('   最终误差: %.3f N\n', pre_train.final_error);
    fprintf('   训练时间: %.1f秒\n', pre_train.training_time);
    
    fprintf('\n2. 完整训练结果:\n');
    fprintf('   训练轮数: %d\n', length(full_train.episode_rewards));
    fprintf('   最终奖励: %.3f\n', full_train.episode_rewards(end));
    fprintf('   最终误差: %.3f N\n', full_train.episode_force_errors(end));
    fprintf('   训练时间: %.1f秒\n', full_train.training_time);
    
    fprintf('\n3. 性能验证结果:\n');
    scenarios = fieldnames(validation);
    
    overall_force_improvement = 0;
    overall_time_improvement = 0;
    valid_scenarios = 0;
    
    for i = 1:length(scenarios)
        scenario = scenarios{i};
        if isfield(validation.(scenario), 'improvement_vs_traditional')
            fprintf('\n   场景: %s\n', scenario);
            fprintf('     GP-DDPG力误差: %.3f N\n', validation.(scenario).ddpg.final_force_error);
            fprintf('     传统方法力误差: %.3f N\n', validation.(scenario).traditional.final_force_error);
            fprintf('     力误差改善: %.1f%%\n', validation.(scenario).improvement_vs_traditional.force);
            fprintf('     调节时间改善: %.1f%%\n', validation.(scenario).improvement_vs_traditional.time);
            
            if isfinite(validation.(scenario).improvement_vs_traditional.force)
                overall_force_improvement = overall_force_improvement + validation.(scenario).improvement_vs_traditional.force;
                valid_scenarios = valid_scenarios + 1;
            end
            if isfinite(validation.(scenario).improvement_vs_traditional.time)
                overall_time_improvement = overall_time_improvement + validation.(scenario).improvement_vs_traditional.time;
            end
        end
    end
    
    if valid_scenarios > 0
        fprintf('\n4. 总体性能改善:\n');
        fprintf('   平均力误差改善: %.1f%%\n', overall_force_improvement / valid_scenarios);
        fprintf('   平均调节时间改善: %.1f%%\n', overall_time_improvement / length(scenarios));
    end
    
    fprintf('\n=== 报告结束 ===\n');
    
    % 保存结果
    timestamp = datestr(now, 'yyyymmdd_HHMMSS');
    filename = sprintf('improved_training_results_%s.mat', timestamp);
    save(filename, 'pre_train', 'full_train', 'validation');
    fprintf('\n结果已保存到: %s\n', filename);
end

% 主执行
main_improved_training();

% 简化的GP-DDPG系统测试脚本
% 用于验证系统组件是否正常工作

function test_ddpg_system()
    fprintf('=== GP-DDPG系统测试 ===\n');
    
    % 清理环境
    close all;
    clc;
    
    try
        % 测试1: 环境初始化
        fprintf('1. 测试环境初始化...\n');
        env = force_control_environment();
        state = env.reset();
        fprintf('   ✓ 环境初始化成功，状态维度: %d\n', length(state));
        
        % 测试2: GP-DDPG控制器初始化
        fprintf('2. 测试GP-DDPG控制器初始化...\n');
        controller = gp_ddpg_controller(6, 2);
        fprintf('   ✓ GP-DDPG控制器初始化成功\n');
        
        % 测试3: 策略切换器初始化
        fprintf('3. 测试策略切换器初始化...\n');
        switcher = policy_switcher([], controller);
        fprintf('   ✓ 策略切换器初始化成功\n');
        
        % 测试4: 基本功能测试
        fprintf('4. 测试基本功能...\n');
        
        % 测试动作获取
        action = controller.get_action(state, true);
        fprintf('   ✓ 动作获取成功: B_theta=%.2f, K_theta=%.2f\n', action(1), action(2));
        
        % 测试环境步进
        [next_state, reward, done] = env.step(action);
        fprintf('   ✓ 环境步进成功: 奖励=%.3f\n', reward);
        
        % 测试策略选择
        [selected_action, policy_used] = switcher.select_action(state, state(5));
        fprintf('   ✓ 策略选择成功: 使用策略=%d\n', policy_used);
        
        % 测试5: 短期训练测试
        fprintf('5. 测试短期训练...\n');
        test_short_training(env, controller, switcher);
        
        % 测试6: 可视化测试
        fprintf('6. 测试可视化功能...\n');
        test_visualization(env);
        
        fprintf('\n=== 所有测试通过 ✓ ===\n');
        fprintf('系统已准备好进行完整训练\n');
        
    catch ME
        fprintf('\n❌ 测试失败: %s\n', ME.message);
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
        
        % 提供修复建议
        provide_fix_suggestions(ME);
    end
end

function test_short_training(env, controller, switcher)
    % 测试短期训练过程
    
    max_steps = 100;  % 短期测试
    
    state = env.reset();
    total_reward = 0;
    
    for step = 1:max_steps
        % 获取动作
        force_error = state(5);
        [action, policy_used] = switcher.select_action(state, force_error);
        
        % 执行动作
        [next_state, reward, done] = env.step(action);
        
        % 计算Q值
        q_value = controller.get_q_value(state, action);
        
        % 存储经验
        controller.add_experience(state, action, reward, next_state, q_value);
        
        % 更新状态
        state = next_state;
        total_reward = total_reward + reward;
        
        % 每20步训练一次
        if step > 20 && mod(step, 20) == 0
            controller.train_critic();
        end
        
        if done
            break;
        end
    end
    
    fprintf('   ✓ 短期训练完成: %d步，总奖励=%.3f\n', step, total_reward);
    
    % 测试GP数据更新
    controller.update_gp_data();
    fprintf('   ✓ GP数据更新完成\n');
    
    % 打印状态
    controller.print_status();
    switcher.print_status();
end

function test_visualization(env)
    % 测试可视化功能
    
    % 运行一个简短的仿真来生成数据
    state = env.reset();
    
    for step = 1:50
        action = [100 + 20*randn(); 1 + 0.5*randn()];  % 随机动作
        action(1) = max(0, min(300, action(1)));
        action(2) = max(0, min(10, action(2)));
        
        [next_state, ~, done] = env.step(action);
        state = next_state;
        
        if done
            break;
        end
    end
    
    % 测试绘图功能
    try
        env.plot_results();
        fprintf('   ✓ 环境可视化测试成功\n');
    catch
        fprintf('   ⚠ 环境可视化测试跳过（可能需要图形界面）\n');
    end
end

function provide_fix_suggestions(ME)
    % 提供修复建议
    
    fprintf('\n=== 修复建议 ===\n');
    
    if contains(ME.message, 'predict')
        fprintf('• predict函数冲突：已在代码中修复，请重新运行\n');
    elseif contains(ME.message, 'Deep Learning Toolbox')
        fprintf('• 缺少Deep Learning Toolbox：使用简化版本实现\n');
    elseif contains(ME.message, 'Out of memory')
        fprintf('• 内存不足：减少缓冲区大小或训练步数\n');
        fprintf('  例如：controller = gp_ddpg_controller(6, 2, ''buffer_size'', 1000);\n');
    elseif contains(ME.message, 'Matrix dimensions')
        fprintf('• 矩阵维度错误：检查状态和动作向量的维度\n');
    else
        fprintf('• 通用建议：\n');
        fprintf('  1. 确保所有.m文件在MATLAB路径中\n');
        fprintf('  2. 检查MATLAB版本兼容性\n');
        fprintf('  3. 尝试重启MATLAB清除缓存\n');
    end
    
    fprintf('\n如果问题持续，请检查具体错误信息并相应调整代码\n');
end

function run_simple_demo()
    % 运行简单演示
    
    fprintf('\n=== 简单演示 ===\n');
    
    % 创建简化的优化器
    try
        optimizer = admittance_optimizer('max_episodes', 5, ...
                                       'max_steps_per_episode', 100);
        
        fprintf('开始简化训练...\n');
        optimizer.train();
        
        fprintf('训练完成，测试性能...\n');
        results = optimizer.test_performance('sinusoidal');
        
        fprintf('测试结果:\n');
        fprintf('  最终力误差: %.3f N\n', results.final_force_error);
        fprintf('  最大力误差: %.3f N\n', results.max_force_error);
        fprintf('  调节时间: %.3f s\n', results.settling_time);
        
    catch ME
        fprintf('演示失败: %s\n', ME.message);
    end
end

% 如果直接运行此脚本，执行测试
if ~nargout
    test_ddpg_system();
    
    % 询问是否运行演示
    user_input = input('\n是否运行简单演示？(y/n): ', 's');
    if strcmpi(user_input, 'y') || strcmpi(user_input, 'yes')
        run_simple_demo();
    end
end

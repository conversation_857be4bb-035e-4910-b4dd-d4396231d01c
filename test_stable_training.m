% 测试数值稳定的训练
clear all;
close all;
clc;
addpath(pwd);

fprintf('=== 测试数值稳定的GP-DDPG训练 ===\n');

try
    % 首先测试环境的数值稳定性
    fprintf('1. 测试环境数值稳定性...\n');
    env = force_control_environment();
    state = env.reset();
    
    % 测试几步看是否有数值爆炸
    for i = 1:10
        action = [100; 1];  % 合理的动作
        [next_state, reward, done] = env.step(action);
        
        if any(~isfinite(next_state)) || ~isfinite(reward)
            fprintf('  数值不稳定在第%d步\n', i);
            fprintf('  状态: [%.3f, %.3f, %.3f, %.3f, %.3f, %.3f]\n', next_state);
            fprintf('  奖励: %.3f\n', reward);
            return;
        end
        
        state = next_state;
    end
    fprintf('  环境数值稳定性测试通过\n');
    
    % 测试短期训练
    fprintf('2. 测试短期训练...\n');
    optimizer = admittance_optimizer('max_episodes', 10, ...
                                   'max_steps_per_episode', 100, ...
                                   'plot_interval', 5);
    
    tic;
    optimizer.train();
    training_time = toc;
    
    fprintf('  短期训练完成，用时: %.1f秒\n', training_time);
    
    % 检查训练结果
    if length(optimizer.episode_rewards) > 0
        final_reward = optimizer.episode_rewards(end);
        final_error = optimizer.episode_force_errors(end);
        
        if isfinite(final_reward) && isfinite(final_error)
            fprintf('  最终奖励: %.3f\n', final_reward);
            fprintf('  最终误差: %.3f N\n', final_error);
            
            % 如果短期训练成功，进行中期训练
            if final_error < 50  % 合理的误差范围
                fprintf('3. 开始中期训练...\n');
                
                optimizer2 = admittance_optimizer('max_episodes', 30, ...
                                                'max_steps_per_episode', 500, ...
                                                'plot_interval', 10);
                
                tic;
                optimizer2.train();
                training_time2 = toc;
                
                fprintf('  中期训练完成，用时: %.1f秒\n', training_time2);
                
                % 性能测试
                fprintf('4. 性能测试...\n');
                scenarios = {'step', 'variable_stiffness'};  % 先测试简单场景
                
                for i = 1:length(scenarios)
                    scenario = scenarios{i};
                    fprintf('  测试场景: %s\n', scenario);
                    
                    result = optimizer2.test_performance(scenario);
                    
                    if isfinite(result.final_force_error) && isfinite(result.settling_time)
                        fprintf('    力误差: %.3f N\n', result.final_force_error);
                        fprintf('    调节时间: %.3f s\n', result.settling_time);
                    else
                        fprintf('    测试结果数值不稳定\n');
                    end
                end
                
                fprintf('数值稳定训练测试成功！\n');
            else
                fprintf('短期训练误差过大: %.3f\n', final_error);
            end
        else
            fprintf('训练结果数值不稳定\n');
        end
    else
        fprintf('训练失败，无结果\n');
    end
    
catch ME
    fprintf('测试失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

classdef force_control_environment < handle
    % 机器人力控制环境类
    % 基于论文中的GP-DDPG算法设计状态空间、动作空间和奖励函数
    
    properties
        % 状态空间参数
        state_dim = 6           % 状态维度：[Xc, Xc_dot, Fe, Fd, delta_F, E]
        action_dim = 2          % 动作维度：[B_theta, K_theta] (导纳参数)
        
        % 环境参数
        Ke = 1000               % 环境刚度
        Xe = 1.0                % 环境位置
        Fd = 20                 % 期望接触力
        
        % 导纳模型参数
        Md = 1.0                % 质量参数
        Bd = 100                % 基础阻尼参数
        Kd = 1e-6               % 基础刚度参数
        
        % 当前状态
        current_state
        
        % 奖励函数参数
        beta = 0.3              % 增加力误差权重
        xi = 0.5                % 降低能耗权重

        % 场景信息
        current_scenario = 'sinusoidal'  % 当前场景
        
        % 动作范围限制
        B_theta_min = 0         % 阻尼参数最小值
        B_theta_max = 300       % 阻尼参数最大值
        K_theta_min = 0         % 刚度参数最小值
        K_theta_max = 10        % 刚度参数最大值
        
        % 仿真参数
        dt = 0.01               % 时间步长
        time = 0                % 当前时间
        
        % 历史数据记录
        state_history
        action_history
        reward_history
    end
    
    methods
        function obj = force_control_environment(varargin)
            % 构造函数
            % 可选参数：Ke, Xe, Fd等环境参数
            
            % 解析输入参数
            p = inputParser;
            addParameter(p, 'Ke', 1000);
            addParameter(p, 'Xe', 1.0);
            addParameter(p, 'Fd', 20);
            addParameter(p, 'Md', 1.0);
            addParameter(p, 'Bd', 100);
            addParameter(p, 'Kd', 1e-6);
            addParameter(p, 'beta', 0.1);
            addParameter(p, 'xi', 1);
            parse(p, varargin{:});
            
            % 设置参数
            obj.Ke = p.Results.Ke;
            obj.Xe = p.Results.Xe;
            obj.Fd = p.Results.Fd;
            obj.Md = p.Results.Md;
            obj.Bd = p.Results.Bd;
            obj.Kd = p.Results.Kd;
            obj.beta = p.Results.beta;
            obj.xi = p.Results.xi;
            
            % 初始化状态
            obj.reset();
        end
        
        function state = reset(obj)
            % 重置环境到初始状态
            % 返回初始状态
            
            obj.time = 0;
            
            % 初始状态：[Xc, Xc_dot, Fe, Fd, delta_F, E]
            Xc = 0.5;           % 初始位置
            Xc_dot = 0.3;       % 初始速度（恒定速度运动）
            Fe = 0;             % 初始接触力
            Fd = obj.Fd;        % 期望接触力
            delta_F = Fd - Fe;  % 力误差
            E = 0;              % 位置误差
            
            obj.current_state = [Xc; Xc_dot; Fe; Fd; delta_F; E];
            
            % 初始化历史记录
            obj.state_history = obj.current_state;
            obj.action_history = [];
            obj.reward_history = [];
            
            state = obj.current_state;
        end
        
        function [next_state, reward, done] = step(obj, action)
            % 执行一步仿真
            % 输入：action - [B_theta, K_theta]
            % 输出：next_state, reward, done
            
            % 限制动作范围
            B_theta = max(obj.B_theta_min, min(obj.B_theta_max, action(1)));
            K_theta = max(obj.K_theta_min, min(obj.K_theta_max, action(2)));
            
            % 当前状态解析
            Xc = obj.current_state(1);
            Xc_dot = obj.current_state(2);
            Fe = obj.current_state(3);
            Fd = obj.current_state(4);
            delta_F = obj.current_state(5);
            E = obj.current_state(6);
            
            % 环境模型：Fe = Ke * (Xc - Xe)
            % 只有当机器人接触环境时才产生接触力
            if Xc >= obj.Xe
                Fe = obj.Ke * (Xc - obj.Xe);
            else
                Fe = 0;
            end
            
            % 导纳模型动力学：Md*Xc_ddot + (Bd + B_theta)*Xc_dot + (Kd + K_theta)*E = delta_F
            % 其中 E = Xc - Xd, delta_F = Fd - Fe
            
            % 计算期望轨迹（简化为恒定速度运动）
            Xd = 0.3 * obj.time;  % 期望轨迹
            Xd_dot = 0.3;         % 期望速度
            
            % 更新位置误差
            E = Xc - Xd;
            
            % 更新力误差
            delta_F = Fd - Fe;
            
            % 导纳模型求解加速度
            % Xc_ddot = (delta_F - (Bd + B_theta)*Xc_dot - (Kd + K_theta)*E) / Md

            % 限制各项以避免数值爆炸
            delta_F = max(-1000, min(1000, delta_F));
            damping_term = (obj.Bd + B_theta) * Xc_dot;
            damping_term = max(-1000, min(1000, damping_term));
            stiffness_term = (obj.Kd + K_theta) * E;
            stiffness_term = max(-1000, min(1000, stiffness_term));

            Xc_ddot = (delta_F - damping_term - stiffness_term) / obj.Md;

            % 限制加速度
            Xc_ddot = max(-100, min(100, Xc_ddot));

            % 数值积分更新状态
            Xc_dot_new = Xc_dot + Xc_ddot * obj.dt;
            Xc_new = Xc + Xc_dot * obj.dt;

            % 限制速度和位置
            Xc_dot_new = max(-10, min(10, Xc_dot_new));
            Xc_new = max(-1, min(2, Xc_new));
            
            % 更新时间
            obj.time = obj.time + obj.dt;
            
            % 构建新状态
            next_state = [Xc_new; Xc_dot_new; Fe; Fd; delta_F; E];
            obj.current_state = next_state;
            
            % 计算奖励
            reward = obj.calculate_reward(action, delta_F, Xc_dot_new);
            
            % 判断是否结束（简单的时间限制）
            done = obj.time >= 15.0;  % 15秒仿真时间
            
            % 记录历史
            obj.state_history = [obj.state_history, next_state];
            obj.action_history = [obj.action_history, [B_theta; K_theta]];
            obj.reward_history = [obj.reward_history, reward];
        end
        
        function reward = calculate_reward(obj, action, delta_F, Xc_dot)
            % 改进的多目标奖励函数
            % 基于论文公式(17)：R = ce + cd，但增加了更多的奖励塑形

            % 获取当前状态信息
            current_state = obj.current_state;
            force_error = abs(delta_F);
            position = current_state(1);
            velocity = Xc_dot;

            % 计算力误差变化（如果有历史数据）
            force_error_change = 0;
            if size(obj.state_history, 2) > 1
                prev_force_error = abs(obj.state_history(5, end));
                force_error_change = force_error - prev_force_error;
            end

            % 根据当前场景调整奖励权重
            [beta_adj, xi_adj] = obj.get_scenario_weights();

            % 1. 力跟踪奖励（主要目标）
            force_tracking_reward = -10 * force_error^2;

            % 2. 收敛速度奖励（鼓励快速减小误差）
            convergence_reward = 0;
            if force_error_change < 0
                % 误差在减小，给予正奖励
                convergence_reward = 5 * abs(force_error_change);
            else
                % 误差在增大，给予负奖励
                convergence_reward = -8 * force_error_change;
            end

            % 3. 平滑动作奖励（避免控制输出抖动）
            B_theta_norm = action(1) / obj.B_theta_max;
            K_theta_norm = action(2) / obj.K_theta_max;
            action_smoothness_reward = -0.1 * (B_theta_norm^2 + K_theta_norm^2);

            % 4. 稳定性奖励（避免过大的位置和速度）
            stability_reward = -0.05 * (position^2 + 0.1 * velocity^2);

            % 5. 早期阶段更注重收敛速度，后期更注重精度
            if force_error > 5
                % 误差较大时，优先考虑收敛速度
                alpha = 0.7;
                beta = 0.3;
            else
                % 误差较小时，优先考虑精度
                alpha = 0.3;
                beta = 0.7;
            end

            % 组合基础奖励
            base_reward = beta * force_tracking_reward + alpha * convergence_reward + ...
                         0.1 * action_smoothness_reward + 0.1 * stability_reward;

            % 6. 额外奖励：当力误差小于阈值时给予额外奖励
            bonus_reward = 0;
            if force_error < 0.5
                bonus_reward = bonus_reward + 10;
            end

            % 7. 惩罚：当力误差过大时给予额外惩罚
            penalty_reward = 0;
            if force_error > 10
                penalty_reward = penalty_reward - 10;
            end

            % 8. 接触奖励（鼓励快速接触环境）
            contact_reward = 0;
            if position >= obj.Xe
                contact_reward = 50 / (1 + force_error/5);
            end

            % 9. 精确跟踪奖励（鼓励力误差小）
            tracking_reward = 0;
            if force_error < 5
                tracking_reward = 100 / (1 + force_error);
            end

            % 10. 场景特定的奖励塑形
            scenario_reward = obj.get_scenario_specific_reward(delta_F, Xc_dot, action);

            % 组合所有奖励
            reward = base_reward + bonus_reward + penalty_reward + contact_reward + ...
                    tracking_reward + scenario_reward;

            % 数值稳定性检查
            if ~isfinite(reward) || abs(reward) > 1e6
                reward = -1000;  % 惩罚数值不稳定的情况
            end
        end
        
        function state = get_state(obj)
            % 获取当前状态
            state = obj.current_state;
        end
        
        function info = get_info(obj)
            % 获取环境信息
            info.time = obj.time;
            info.contact_force = obj.current_state(3);
            info.desired_force = obj.current_state(4);
            info.force_error = obj.current_state(5);
            info.position = obj.current_state(1);
            info.velocity = obj.current_state(2);
            info.position_error = obj.current_state(6);
            info.in_contact = obj.current_state(1) >= obj.Xe;
        end
        
        function set_environment_variation(obj, variation_type, varargin)
            % 设置环境变化
            % variation_type: 'sinusoidal', 'step', 'exponential_sine'
            
            switch variation_type
                case 'sinusoidal'
                    % 正弦变化环境位置
                    amplitude = 0.1;
                    frequency = 0.5;
                    if ~isempty(varargin)
                        amplitude = varargin{1};
                        if length(varargin) > 1
                            frequency = varargin{2};
                        end
                    end
                    obj.Xe = 1.0 + amplitude * sin(2 * pi * frequency * obj.time);
                    
                case 'step'
                    % 阶跃变化期望力
                    if obj.time > 7.5
                        obj.Fd = 30;  % 从20N变为30N
                    else
                        obj.Fd = 20;
                    end
                    
                case 'exponential_sine'
                    % 指数正弦变化（论文中的复杂环境）
                    obj.Xe = 0.5 * exp(-0.1 * obj.time) * sin(2 * pi * 0.2 * obj.time) + 0.005 * obj.time;
                    
                case 'variable_stiffness'
                    % 变刚度环境
                    if obj.time < 5
                        obj.Ke = 1000;
                    elseif obj.time < 10
                        obj.Ke = 2000;
                    else
                        obj.Ke = 500;
                    end
            end
        end
        
        function plot_results(obj)
            % 绘制仿真结果
            if isempty(obj.state_history)
                warning('没有历史数据可绘制');
                return;
            end
            
            time_vec = 0:obj.dt:(size(obj.state_history, 2)-1)*obj.dt;
            
            figure;
            
            % 位置跟踪
            subplot(2, 3, 1);
            plot(time_vec, obj.state_history(1, :), 'b-', 'LineWidth', 2);
            hold on;
            plot(time_vec, 0.3 * time_vec, 'r--', 'LineWidth', 1.5);
            xlabel('时间 (s)');
            ylabel('位置 (m)');
            title('位置跟踪');
            legend('实际位置', '期望位置');
            grid on;
            
            % 接触力跟踪
            subplot(2, 3, 2);
            plot(time_vec, obj.state_history(3, :), 'b-', 'LineWidth', 2);
            hold on;
            plot(time_vec, obj.state_history(4, :), 'r--', 'LineWidth', 1.5);
            xlabel('时间 (s)');
            ylabel('力 (N)');
            title('接触力跟踪');
            legend('实际力', '期望力');
            grid on;
            
            % 力误差
            subplot(2, 3, 3);
            plot(time_vec, obj.state_history(5, :), 'r-', 'LineWidth', 2);
            xlabel('时间 (s)');
            ylabel('力误差 (N)');
            title('力跟踪误差');
            grid on;
            
            % 导纳参数变化
            if ~isempty(obj.action_history)
                subplot(2, 3, 4);
                plot(time_vec(1:end-1), obj.action_history(1, :), 'g-', 'LineWidth', 2);
                xlabel('时间 (s)');
                ylabel('B_θ');
                title('阻尼参数变化');
                grid on;
                
                subplot(2, 3, 5);
                plot(time_vec(1:end-1), obj.action_history(2, :), 'm-', 'LineWidth', 2);
                xlabel('时间 (s)');
                ylabel('K_θ');
                title('刚度参数变化');
                grid on;
            end
            
            % 奖励变化
            if ~isempty(obj.reward_history)
                subplot(2, 3, 6);
                plot(time_vec(1:end-1), obj.reward_history, 'k-', 'LineWidth', 2);
                xlabel('时间 (s)');
                ylabel('奖励');
                title('奖励函数变化');
                grid on;
            end
        end

        function [beta_adj, xi_adj] = get_scenario_weights(obj)
            % 根据场景调整奖励权重（改进版）

            switch obj.current_scenario
                case 'step'
                    % step场景：强调快速响应和超调控制
                    beta_adj = 0.8;  % 增加力误差权重
                    xi_adj = 0.15;   % 降低能耗权重，允许更大的控制输出

                case 'variable_stiffness'
                    % variable_stiffness场景：强调适应性和鲁棒性
                    beta_adj = 0.65; % 平衡力误差权重
                    xi_adj = 0.25;   % 适中能耗权重

                case 'sinusoidal'
                    % sinusoidal场景：强调跟踪性能和平滑性
                    beta_adj = 0.6;  % 平衡权重
                    xi_adj = 0.3;    % 适中能耗权重

                otherwise
                    % 默认权重
                    beta_adj = obj.beta;
                    xi_adj = obj.xi;
            end
        end

        function scenario_reward = get_scenario_specific_reward(obj, delta_F, Xc_dot, action)
            % 改进的场景特定奖励塑形

            scenario_reward = 0;
            force_error = abs(delta_F);

            switch obj.current_scenario
                case 'step'
                    % step场景：奖励快速响应、低超调和稳定性

                    % 检测超调
                    target_force = obj.current_state(4);
                    actual_force = obj.current_state(3);
                    if actual_force > target_force * 1.05  % 超调超过5%
                        overshoot_penalty = -50 * (actual_force/target_force - 1);
                        scenario_reward = scenario_reward + overshoot_penalty;
                    end

                    % 快速响应奖励
                    if force_error < 2 && abs(Xc_dot) < 0.5
                        scenario_reward = scenario_reward + 250;  % 快速稳定奖励
                    elseif force_error < 5
                        scenario_reward = scenario_reward + 80 / (1 + force_error);  % 响应奖励
                    end

                    % 阻尼参数奖励（step场景需要较高阻尼）
                    if action(1) > 150 && action(1) < 250
                        scenario_reward = scenario_reward + 30;  % 合适的阻尼参数
                    end

                case 'variable_stiffness'
                    % variable_stiffness场景：奖励适应性和鲁棒性

                    % 基础适应性奖励
                    adaptation_reward = 120 / (1 + force_error + 0.5 * abs(Xc_dot));
                    scenario_reward = scenario_reward + adaptation_reward;

                    % 环境刚度变化检测
                    if obj.time > 4.9 && obj.time < 5.1 || obj.time > 9.9 && obj.time < 10.1
                        % 刚度变化点附近，奖励快速适应
                        if force_error < 5
                            scenario_reward = scenario_reward + 200;  % 刚度变化适应奖励
                        end
                    end

                    % 奖励参数调整的合理性和适应性
                    if obj.time < 5
                        % 低刚度阶段
                        optimal_B = 120;
                    elseif obj.time < 10
                        % 高刚度阶段
                        optimal_B = 200;
                    else
                        % 低刚度阶段
                        optimal_B = 80;
                    end

                    B_adaptation = 50 / (1 + abs(action(1) - optimal_B)/50);
                    scenario_reward = scenario_reward + B_adaptation;

                case 'sinusoidal'
                    % sinusoidal场景：奖励跟踪性能和平滑性

                    % 改进的跟踪质量评估
                    tracking_quality = 1 / (1 + force_error^2/10);
                    smoothness_quality = 1 / (1 + abs(Xc_dot)^2/4);

                    % 组合奖励，更注重跟踪性能
                    scenario_reward = 200 * (0.8 * tracking_quality + 0.2 * smoothness_quality);

                    % 奖励参数的周期性调整（适应正弦环境）
                    if mod(obj.time, 2) < 1  % 环境位置上升阶段
                        if action(1) < 120 && action(2) > 0.5
                            scenario_reward = scenario_reward + 40;  % 低阻尼高刚度更适合上升阶段
                        end
                    else  % 环境位置下降阶段
                        if action(1) > 120 && action(2) < 0.5
                            scenario_reward = scenario_reward + 40;  % 高阻尼低刚度更适合下降阶段
                        end
                    end

                otherwise
                    scenario_reward = 0;
            end
        end
    end
end

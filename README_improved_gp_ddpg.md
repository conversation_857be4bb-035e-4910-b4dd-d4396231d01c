# 改进的GP-DDPG导纳控制算法

## 概述

本项目实现了一个改进的GP-DDPG（Gaussian Process Deep Deterministic Policy Gradient）导纳控制算法，针对原始实现中的性能问题进行了全面优化。

## 主要改进

### 1. 奖励函数优化
- **多目标奖励设计**：平衡力跟踪精度和收敛速度
- **场景自适应奖励**：针对不同场景（sinusoidal、step、variable_stiffness）调整奖励权重
- **动态权重调整**：根据误差大小动态调整精度和速度的权重

### 2. 网络结构改进
- **更深更宽的网络**：增加网络深度和宽度以提高表达能力
- **批归一化**：加速训练收敛并提高稳定性
- **TD3算法**：使用双Critic网络减少Q值过估计

### 3. 训练策略优化
- **课程学习**：从简单到复杂的分阶段训练策略
- **改进的探索策略**：使用Ornstein-Uhlenbeck过程和自适应噪声
- **优先经验回放**：基于TD误差的优先级采样

### 4. 超参数优化
- **自适应学习率**：根据训练进度和性能变化调整学习率
- **动态探索衰减**：随训练进行逐步降低探索噪声

## 预期性能改善

| 性能指标 | 预期改善 |
|---------|---------|
| 力跟踪精度 (sinusoidal) | 64% |
| 力跟踪精度 (step) | 70% |
| 力跟踪精度 (variable_stiffness) | 60% |
| 收敛速度 (sinusoidal) | 71% |
| 收敛速度 (step) | 75% |
| 收敛速度 (variable_stiffness) | 68% |

## 文件结构

```
├── admittance_optimizer.m          # 改进的优化器主类
├── force_control_environment.m     # 改进的环境类（奖励函数）
├── PrioritizedReplayBuffer.m       # 优先经验回放缓冲区
├── run_improved_gp_ddpg.m          # 运行改进算法的主脚本
├── test_improved_gp_ddpg.m         # 性能对比测试脚本
├── run_ddpg_admittance_control.m   # 原始对比脚本（已修复柱状图问题）
└── README_improved_gp_ddpg.md      # 本说明文档
```

## 使用方法

### 1. 运行改进的算法

```matlab
% 运行改进的GP-DDPG算法
run_improved_gp_ddpg();
```

### 2. 性能对比测试

```matlab
% 对比原始版本和改进版本
test_improved_gp_ddpg();
```

### 3. 自定义参数训练

```matlab
% 创建自定义优化器
optimizer = admittance_optimizer('max_episodes', 100, ...
                                'max_steps_per_episode', 1500, ...
                                'use_curriculum', true, ...
                                'exploration_noise_scale', 0.3, ...
                                'learning_rate_actor', 1e-4, ...
                                'learning_rate_critic', 1e-3);

% 开始训练
optimizer.train();

% 测试性能
result = optimizer.test_performance('sinusoidal');
```

## 关键参数说明

### 课程学习参数
- `use_curriculum`: 是否使用课程学习（推荐：true）
- `curriculum_level`: 当前课程级别（1-5）

### 探索策略参数
- `exploration_noise_scale`: 探索噪声幅度（推荐：0.3）
- `ou_theta`: OU过程参数（推荐：0.15）
- `ou_sigma`: OU过程噪声幅度（推荐：0.2）

### 网络训练参数
- `learning_rate_actor`: Actor学习率（推荐：1e-4）
- `learning_rate_critic`: Critic学习率（推荐：1e-3）
- `batch_size`: 批次大小（推荐：256）
- `gamma`: 折扣因子（推荐：0.99）
- `tau`: 软更新参数（推荐：0.005）

### TD3特定参数
- `policy_noise`: 策略噪声（推荐：0.2）
- `noise_clip`: 噪声裁剪（推荐：0.5）
- `policy_update_freq`: 策略更新频率（推荐：2）

## 课程学习阶段

1. **阶段1（预训练）**：简单场景，高探索率
2. **阶段2（主训练）**：中等场景，平衡探索与利用
3. **阶段3（复杂训练）**：复杂场景，降低探索率
4. **阶段4（高级训练）**：变刚度场景，精细调整
5. **阶段5（综合训练）**：随机场景组合，最终优化

## 性能监控

训练过程中会自动绘制以下图表：
- 训练奖励变化
- 力跟踪误差收敛
- 收敛时间变化
- Q值学习过程
- Critic网络损失
- Actor网络损失

## 故障排除

### 常见问题

1. **训练不收敛**
   - 检查学习率设置
   - 确保奖励函数设计合理
   - 增加训练轮数

2. **性能不如预期**
   - 启用课程学习
   - 调整探索策略参数
   - 检查网络结构设置

3. **内存不足**
   - 减少经验回放缓冲区大小
   - 降低批次大小
   - 减少网络隐藏层大小

### 调试技巧

- 使用`plot_training_results()`监控训练过程
- 检查`episode_rewards`和`episode_force_errors`的变化趋势
- 使用较小的参数进行快速测试

## 技术细节

### 改进的奖励函数

```matlab
reward = beta * force_tracking_reward + alpha * convergence_reward + 
         0.1 * action_smoothness_reward + 0.1 * stability_reward + 
         bonus_reward + penalty_reward + contact_reward + 
         tracking_reward + scenario_reward;
```

### 课程学习策略

每个级别都有特定的：
- 场景类型
- 目标力设置
- 环境刚度
- 收敛阈值

### 优先经验回放

基于TD误差计算优先级：
```matlab
priority = (abs(td_error) + epsilon)^alpha
```

## 贡献

欢迎提交问题和改进建议。主要改进方向：
- 更先进的网络结构
- 更好的探索策略
- 更精细的奖励函数设计
- 更高效的训练算法

## 许可证

本项目遵循MIT许可证。

> [言 ***actuators***](https://www.mdpi.com/journal/actuators)

[![](./media/media/image1.png){width="0.6222364391951006in"
height="0.5001246719160105in"}](https://www.mdpi.com)

> Article
>
> **An Admittance Parameter Optimization Method Based on Reinforcement
> Learning for Robot Force Control**
>
> **Xiaoyi Hu
> 1**[![](./media/media/image2.png){width="0.13290244969378828in"
> height="0.1259831583552056in"}](https://orcid.org/0009-0008-9718-8228)**,
> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>,\*, <PERSON><PERSON>, <PERSON><PERSON> 1
> and <PERSON><PERSON> 3**
>
> ![](./media/media/image3.png){width="0.7363877952755905in"
> height="0.2638888888888889in"}[巴 check
> for](https://www.mdpi.com/article/10.3390/act13090354?type=check_update&version=1)
>
> **Citation:** <PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON><PERSON>; <PERSON>, P.; Jia,
>
> B.; <PERSON>, Y.; Li, L.; Duan, S. An
>
> Admittance Parameter Optimization Method Based on Reinforcement
>
> Learning for Robot Force Control.
>
> Actuators **2024**, 13, 354.
> [https://](https://doi.org/10.3390/act13090354)
> [doi.org/10.3390/act13090354](https://doi.org/10.3390/act13090354)
>
> Received: 16 July 2024
>
> Revised: 2 September 2024
>
> Accepted: 9 September 2024
>
> Published: 12 September 2024

![](./media/media/image4.jpeg){width="0.7949671916010499in"
height="0.27689195100612424in"}

> **Copyright:** © 2024 by the authors. Licensee MDPI, Basel,
> Switzerland. This article is an open access article distributed under
> the terms and conditions of the Creative Commons Attribution (CC BY)
> license [(https://](https://creativecommons.org/licenses/by/4.0/)
> [creativecommons.org/licenses/by/](https://creativecommons.org/licenses/by/4.0/)
>
> 4.0/).
>
> 1 College of Mechanical and Electrical Engineering, Changchun
> University of Science and Technology, Changchun 130022, China;
> <EMAIL> (X.H.); <EMAIL> (Y.L.);
>
> <EMAIL> (L.L.)
>
> 2 Avic Xi'an Aircraft Industry Group Co., Ltd., Xi'an 710089, China;
> <EMAIL> (G.L.);
>
> <EMAIL> (P.R.)
>
> 3 Sichuan Huachuan Industry Co., Ltd., Chengdu 610100, China;
> <EMAIL>

**\*** Correspondence: <EMAIL>

**Abstract:** When a robot performs tasks such as assembly or
human--robot interaction, it is inevitable for it to collide with the
unknown environment, resulting in potential safety hazards. In order to
improve the compliance of robots to cope with unknown environments and
enhance their intelligence in contact force-sensitive tasks, this paper
proposes an improved admittance force control method, which combines
classical adaptive control and machine learning methods to make them use
their respective advantages in different stages of training and,
ultimately, achieve better performance. In addition, this paper proposes
an improved Deep Deterministic Policy Gradient (DDPG)-based optimizer,
which is combined with the Gaussian process (GP) model to optimize the
admittance parameters. In order to verify the feasibility of the
algorithm, simulations and experiments are carried out in MATLAB and on
a UR10e robot, respectively. The experimental results show that the
algorithm improves the convergence speed by 33% in comparison to the
general model-free learning method, and has better control performance
and robustness. Finally, the adjustment time required by the algorithm
is 44% shorter than that of classical adaptive admittance control.

> **Keywords:** reinforcement learning; admittance control; industrial
> robot; robot force control
>
> **1. Introduction**
>
> As industrial robot applications continue to expand
> \[[1](#bookmark1)--[3](#bookmark2)\], the operational process of
> industrial robots has become increasingly independent of human
> intervention. It is imperative that robots possess the capacity to
> interact with their environments in a stable and reliable manner.
> However, in environments with high complexity and uncertainty, it
> remains challenging to ensure the reliable and accurate interactive
> control of the robot \[[4](#bookmark3)\]. The models of commercial
> robots are highly precise \[[5](#bookmark4)\], but the precise
> modeling of the environment is usually not available. The occurrence
> of sudden changes in the environment can result in the generation of
> unpredictable deviations in the interaction.
>
> Compliance control is widely used in robot interaction control
> \[[6](#bookmark4)\]. This kind of method also pays attention to
> contact force tracking while performing position control. Common
> compliance control methods can be roughly classified as hybrid
> position/force control \[[7](#bookmark6)\] and impedance control
> \[[8](#bookmark8)\]. Position-based impedance control is also known as
> admittance control. The accuracy of hybrid position/force control is
> reduced because it ignores the dynamic coupling between the robot and
> the environment \[[9](#bookmark8)\]. Impedance control establishes the
> dynamic relationship between robot motion and contact force, and can
> achieve very accurate control with simple modeling.

Adaptive control can effectively deal with the complex situation of
uncertainty \[[10](#bookmark9)\].

> Adaptive impedance control is an extension of classical impedance
> control. It continuously
>
> Actuators **2024**, 13, 354. <https://doi.org/10.3390/act13090354>
> <https://www.mdpi.com/journal/actuators>
>
> adjusts the impedance parameters to adapt to dynamic changes through
> feedback infor- mation, showing higher flexibility and robustness. So
> far, there have been many research results on such algorithms
> \[[11](#bookmark10)--[14](#bookmark11)\].
>
> With the rapid development of artificial intelligence technology,
> machine learning methods are gradually being applied to robot force
> control technology. Compared with classical control, intelligent
> control is more inclusive of the unknowns in the model. In the early
> days, imitation learning method was used to build the model by
> collecting the demon- stration experience of human experts
> \[[15](#bookmark11)--[19](#bookmark13)\]. A neural network was used
> for parameter estimation \[[20](#bookmark14)\], contact force
> prediction \[[21](#bookmark15)\] and system unknowns prediction
> \[[22](#bookmark16),[23](#bookmark17)\]. Imi- tation learning was used
> to improve the portability of the controller \[[24](#bookmark18)\]. In
> recent years, neural networks have received more attention, and their
> application to variable impedance control has also been widely
> developed \[[25](#bookmark19)--[28](#bookmark20)\].
>
> Reinforcement learning (RL) is another machine learning technique.
> Unlike deep learning, which relies on a large amount of prior
> knowledge, reinforcement learning is semi-supervised learning. It
> accumulates experience through repeated attempts to achieve autonomous
> learning. In \[[29](#bookmark21)\], the prior knowledge of the
> operator is introduced, and the reinforcement learning, fuzzy
> technology, and neural network are combined to propose a reinforcement
> learning fuzzy neural network architecture based on the actor--critic
> ar- chitecture, which is used to learn the parameters of the variable
> admittance controller. In \[[30](#bookmark22)\], the artificial neural
> network is used to establish the dynamics of human--computer
> interaction, and the model is used for reinforcement learning,
> combining a cross-entropy method and model prediction control. In
> \[[31](#bookmark23)\], a policy iteration with constraint embedding is
> proposed to ensure the semi-definiteness of the policy evaluation
> function and to sim- plify its form. In \[[32](#bookmark24)\], the
> admittance parameters are directly updated through Q-learning. In
> \[[33](#bookmark25)\], a reinforcement learning variable impedance
> control with stability guarantee is proposed. The extended
> cross-entropy reinforcement learning method is trained to make direct
> decisions on the state variables. Its stability comes from the
> conservative design based on the stability of the constant impedance
> controller. In \[[34](#bookmark26),[35](#bookmark27)\], model-based
> rein- forcement learning methods are used to approximate the system's
> environmental change model to predict the evolution of the system
> state in the future.
>
> Usually, the law of environmental change is unknown to the optimizer.
> In practice, environmental changes are usually estimated by designing
> dynamic models or designing model-free reinforcement learning to
> directly approximate the state results after envi- ronmental changes.
> The difficulty in model-based reinforcement learning is that, due to
> uncertainty, a good dynamics model is usually difficult to design
> \[[36](#bookmark28),[37](#bookmark29)\]. In model-free methods, the
> value function is directly synthesized, which requires a lot of
> training time and data.
>
> Deep Deterministic Policy Gradient (DDPG) \[[38](#bookmark29)\] is one
> of the reinforcement learning methods that is based on actor--critic
> architecture. It combines the advantages of the DQN algorithm and DPG
> algorithm. It not only solves the problem that random policy is
> difficult to sample in complex action space, but also makes up for the
> defect that DQN cannot deal with high-dimensional continuous action
> space. It is very suitable for applications with high dimensions and
> large task space such as robot operation.
>
> The Gaussian process (GP) model is another kind of learner that has
> been combined with reinforcement learning
> \[[34](#bookmark26),[35](#bookmark27),[39](#bookmark31),[40](#bookmark32)\].
> Compared with other regression models, the GP model has a better
> approximating effect when the data set is smaller and contains the
> uncertainty of the model. Such characteristics are more suitable for
> relatively small changes during industrial robot operations. We use it
> to approximate the policy function to achieve the purpose of reducing
> the amount of data and fast convergence.
>
> In this paper, an intelligent variable admittance control method,
> combining classical control theory and reinforcement learning theory,
> is proposed and applied to the interactive compliance control of
> industrial robots in a changing environment. By fully considering the
> results of classical control and an intelligent optimizer, the
> parameters of impedance control are adjusted, which improves the
> limitation of the model-free reinforcement learning
>
> method. This method does not need to model the complex physical
> environment and robot, and does not require long-term experience
> accumulation, which improves safety and learning efficiency. The GP
> model is combined with the DDPG in the reinforcement learning system
> and applied to the approximation policy function. The improved
> algorithm requires fewer data sets and hyperparameters, and has higher
> learning efficiency and faster convergence.
>
> The rest of this paper is as follows: Section
> [2](#bookmark33)introduces the classical admittance control and states
> the problems to be studied. Section [3](#bookmark34)describes the
> variable admittance control algorithm proposed in this paper in
> detail. Section [4](#bookmark35)shows the results of simulations and
> experiments in detail. Finally, in Section [5](#bookmark36), we
> summarize the research results of this paper.
>
> []{#bookmark33 .anchor}**2. Problem Statement**
>
> 2.1. Classical Admittance Control
>
> Hogan \[[8](#bookmark8)\] proposed impedance control. The core idea of
> impedance control is to model the control object as a system with
> spring--mass--damping characteristics, and the relationship between
> contact force and motion state is characterized by the dynamic equa-
> tion, so as to realize the control of the dynamic relationship between
> the two. In Cartesian space, the typical impedance model of a robot is
> expressed as

Md(c − d) + Bd(X˙c − X˙d) + K d(Xc − Xd) = Fd − Fe (1)

> Md, Bd, K d ∈ Rn ×n (n is the dimension of the minimal coordinate set)
> represent the mass, damping, and stiffness of the assumed system,
> which are positive definite.

Xd, Xc ∈ Rn ×1 are the reference trajectory and the corrected
trajectory; X˙d, X˙c, d, c are

> their velocity and acceleration, respectively. Fe ∈ Rn ×1 is the
> actual contact force / torque, and Fd ∈ Rn ×1 is the expected one.
>
> Admittance control is impedance control based on motion, which
> realizes the mapping from generalized force to generalized position.
> In general, the structure consists of a force control outer loop and a
> position control inner loop, as shown in Figure [1](#bookmark37). The
> force controller receives the feedback from the force sensor, and
> outputs the corrected trajectory Xc. After inverse kinematics mapping,
> the joint trajectory q c is tracked by the joint controller inner
> loop. Nowadays, compared with the constant parameter admittance
> control method, the admittance control of variable parameters must be
> developed to meet the increasingly []{#bookmark37 .anchor}complex work
> requirements.

![](./media/media/image5.jpeg){width="6.69305883639545in"
height="1.5933639545056868in"}

> **Figure 1.** Classical admittance control system structure.
>
> 2.2. Evolution of Contact Force
>
> We model the robot as an admittance model with a multi-dimensional
> spring--mass-- damping system. In general, the contact between the
> robot and the environment is divided into three phases, as shown in
> Figure [2](#bookmark38).
>
> On the left side of Figure [2](#bookmark38), a schematic diagram of
> three phases is shown, and, on the right side, the evolution of
> contact force in different phases is shown. In the free movement
> phase, the robot has not been in contact with the environment. At this
> time, the contact force Fe = 0; the admittance model changes the
> trajectory under the guidance of Fd and gradually drives the robot to
> move in the direction of Fe − Fd → 0. In the oscillation
>
> phase, the robot has a strong nonlinear collision with the environment
> because the location and characteristics of the environment are
> unknown to the control system. However, due to the adjustment of the
> trajectory by the admittance control, such oscillations will gradually
> converge. In the stable contact phase, the contact force between the
> two tends to be stable, []{#bookmark38 .anchor}and Fe − Fd = 0.

![](./media/media/image6.jpeg){width="6.299330708661417in"
height="4.063063210848644in"}

> **Figure 2.** Robot and environment contact process model.
>
> In the oscillation phase of contact, the high force overshoot is not
> expected. At the same time, we hope that the contact force can quickly
> stabilize and make the steady-state error tend to zero. The following
> part of the paper will introduce the research on achieving this goal.
>
> []{#bookmark34 .anchor}**3. The Proposed Method**
>
> In this paper, an intelligent variable admittance control method is
> designed. In this method, the classical adaptive admittance controller
> is used as the initial policy to generate the previous experience
> (similar to the experience of human experts), in combination with a
> variable admittance controller trained by a reinforcement learning
> method (intelligent policy).
>
> 3.1. Intelligent Admittance Control System
>
> As shown in Figure [3](#bookmark39), the selector outputs variable
> admittance parameters by se- lecting two policies. For security, in
> the early phase of training, it tends to select the action generated
> by the initial policy and record the generated data set; after a
> period, the transformation from selecting the initial policy to
> selecting the learning policy is real- ized. In Figure
> [3](#bookmark39), Md, Bd, K d, Xc, Xd, Fd, and Fe are defined as
> Equation (1). B*θ*, K*θ* are the variable admittance parameters
> designed in this paper. *πθ*, *π*I are the intelligent policy and
> initial policy, respectively, and q c is the joint variable of Xc
> solved by inverse kinematics. After the adjustment of the robot joint
> controller, the real joint position of the robot is qm, while Xm is
> the forward kinematics solution of qm .

![](./media/media/image7.jpeg){width="6.102331583552056in"
height="2.2616447944007in"}

> []{#bookmark39 .anchor}**Figure 3.** Intelligent admittance control
> system structure.
>
> []{#bookmark40 .anchor}3.2. The Establishment of Robot Model and
> Environment Model
>
> The task environment is modeled as a first-order linear system (a
> linear spring):

Fe = Ke(Xc --- Xe) (2)

> where Xe is the environmental position; the definition of Xc is the
> same as Equation (1).

The robot is modeled as a classical admittance model, as shown in
Equation (1).

> Suppose that the trajectory tracking error of the joint controller is
> zero, i.e., Xc = Xm . Let E = Xc --- Xd and ∆F = Fd --- Fe; then, the
> admittance formula of the robot can be written as

Md + B dE˙ + KdE = ∆F (3)

> With E as the output and ∆F as the input, the second-order system
> shown in Equation (3) has a transfer function H(s) = E(s)/∆F(s) =
> 1/(Md · s2 + Bd · s + K d). ∆F is expected to gradually approach 0;
> for ∆F, there is
>
> ∆F=Fd --- Fe =Fd --- Ke(Xc --- Xe)

= Fd --- Ke(Xd+ ∆F · H(s) --- Xe) (4)

> When the system is in contact steady state, it has s → 0. The force
> error ∆Fss is expressed as

![](./media/media/image8.png){width="3.782269247594051in"
height="0.34537948381452316in"}

> To make ∆Fss → 0, we need to satisfy

![](./media/media/image9.png){width="0.8604560367454068in"
height="0.3061646981627297in"} (6)

> Under the uncertainty conditions considered in this paper, Equation
> (6) cannot be satisfied directly due to the unknown values of Xe and
> Ke, and the steady state of the force error will always exist. To
> reduce the error, the disturbance caused by environmental uncertainty
> must be considered. It is assumed that the disturbance caused by
> environmental changes, including deformation caused by contact force,
> is *δ*E and the disturbance of

interaction force caused by *δ*E is *δ*F = Ke · *δ*E. Let = E + *δ*E and
= ∆F + *δ*F; then,

> it becomes

Md![](./media/media/image10.png){width="9.658027121609798e-2in"
height="0.19751531058617672in"} +
Bd![](./media/media/image11.png){width="9.658027121609798e-2in"
height="0.19751531058617672in"} + K d = (7)

> Md, Bd, and K d are classical admittance parameters that are constant.
> Next, variable admittance parameters are designed to compensate for
> the disturbance. It should be noted that the change in Md is not
> conducive to the stability of the system \[[13](#bookmark41)\]. Thus,
> in this paper, we do not consider changing it. Let the variable
> admittance parameters be B*θ* and K*θ*; then, it becomes

Md + B dE˙ + KdE = ΔF ---
B*θ*![](./media/media/image12.png){width="9.972659667541557e-2in"
height="0.19752843394575678in"} --- K*θ* + *δ*F --- (Md · *δ* --- B dE˙
--- KdE) (8)

> Let B*θ*![](./media/media/image13.png){width="9.974081364829396e-2in"
> height="0.19752843394575678in"} + K*θ* → *δ*F --- (Md · *δ* --- B dE˙
> --- KdE), then ΔFss → 0 holds when t → ∞ .
>
> 3.3. Intelligent Policy
>
> The intelligent policy *πθ* is a modeled as a learner of the damping
> parameter B*θ*, which is modeled as a Gaussian process model. Suppose
> that the data set D = {X, Y}, where X, Y are the optimal/sub-optimal
> state--action pairs in the experience pool H, X = {S1, · · · , S Ne},
> Y = {A1, · · · , ANe}, and Ai = B*θ*i is the action corresponding to
> si. The Gaussian process is an exploration of f (X). It is generally
> modeled as a normal distribution with zero mean, which is uniquely
> determined by the covariance function. Considering the independent and
> identically distributed noise N(0, *σ*n2 ), Y is denoted by

Y = f (X) + N(0, *σ*n2 ) ∼ N(0, K(X, X) + *σ*n2 I) (9)

> where K(·) is the covariance matrix and I is the identity matrix.
>
> For X′, the noiseless prediction function f′(X′) can be expressed as

f′ ∼ N(0, K(X′, X′)) (10)

Under the condition of D, Y and f′ satisfy the multivariate joint
Gaussian distribution.

> From the Bayes Rule, we can obtain
>
> p(f′ \| X′, D) ∼ N(u, Σ); (11) u = k(X′, X)(k(X, X) + *σ*2 I)---1Y
>
> Σ = K(X′, X′) --- K(X′, X)(K(X, X) + *σ*2 I)---1K(X, X′)
>
> At time t, for *πθ*, X′ is the state St and the corresponding output
> of GP is At = B*θ*t. Then, At takes the mean of the posterior
> distribution:

At = u = K(St, X)(K(X, X) + *σ*n2 I)---1 Y (12)

For each element k(xi, x) in the covariance matrix K(X, X′), we select
the squared

> exponential kernel function:

![](./media/media/image14.png){width="1.849834864391951in"
height="0.47540791776027996in"} (13)

where xi, x are one-dimensional elements in X and X′, respectively. *σ*f
, li,j are the hyperpa-

> rameters of the kernel function. *σ*f is the sample variance, and d(·)
> represents the Euclidean distance measure between features.
>
> To ensure the exploration in the early stage, noise *η* is introduced;
> then, *πθ* is denoted by
>
> *πθ* = {K*θ*, B*θ*}

B*θ* = u (St, *θπ* ) + *η* (14)

> = B*θ*
>
> where K*θ* is a constant. In order to meet the stability conditions of
> the admittance system described in \[[41](#bookmark42)\], K*θ* is also
> limited as K*θ* ≻ 0. *θπ* is the hyperparameter of *πθ*, which
> includes D = {X, Y} and the hyperparameter {*σ*f, L, *σ*n } of the
> kernel function itself. *θπ* = {*θ*s, *θ*k } = {X, Y, *σ*f, L, *σ*n },
> *θ*s ={X, Y}, *θ*k ={*σ*f, L, *σ*n }, and L = {l1,1, . . . , li,j, . .
> . }.
>
> []{#bookmark43 .anchor}3.4. Initial Policy
>
> The initial policy *π*I of this paper adopts the adaptive control law
> designed in \[[13](#bookmark41)\]. In that paper, it is assumed that
> the robot is decoupled; the single-dimensional control law is
> expressed as follows :
>
> *π*I = {kI, bI};
>
> kI = 0;

b![](./media/media/image15.png){width="6.854877515310587e-2in"
height="0.24678915135608048in"}
![](./media/media/image16.png){width="0.1208672353455818in"
height="0.24940507436570428in"}f(r)s![](./media/media/image17.png){width="0.1765343394575678in"
height="0.28150590551181104in"}[b]{.underline}![](./media/media/image18.png){width="6.987970253718286e-2in"
height="0.260171697287839in"}[t;]{.underline} Φ(t) (15)

> {( Φ(t) =
> Φ![](./media/media/image19.png){width="4.374015748031496e-2in"
> height="0.11775918635170604in"}(()− *λ*) + *σ*
> ![](./media/media/image20.png){width="0.8315890201224847in"
> height="0.2473228346456693in"}
>
> where the definition of
> ![](./media/media/image21.png){width="5.825021872265967e-2in"
> height="0.16964020122484688in"}.(t), fd, fe are as in Section
> [3.2](#bookmark40), i.e., of a one-dimensional form. bfirst is the
> one-dimensional damping initial value, Δb is a time-varying quantity,
> t is the real time, *λ* is the sampling rate, and *σ* is the learning
> rate.
>
> Through the compensation of the adaptive variable damping control law,
> Δfss → 0 is established at steady state. The stability of the control
> law has been proven by the author of that article. Although the
> classical adaptive admittance control law is more robust than the
> constant admittance control, the experiment shows that its effect is
> still limited.
>
> 3.5. Intelligent Admittance Policy Integrated with Classical Control
>
> Finally, the policy *π* of the intelligent admittance controller is
> defined as *π* : x I→ u = *π*(x, t, *θ*):

*π* = { ;; ![](./media/media/image22.png){width="7.207020997375328e-2in"
height="0.2768875765529309in"}s![](./media/media/image23.png){width="0.12197944006999126in"
height="0.28719378827646547in"} *ε* or ΔFss ∈ U(0, *δ*) ; *ε* ≥ 0 (16)

*ε* is a constant positive real number, and *α* 1 is a time-negative
correlation function.

> When the training begins, *α* 1 is larger and *π* has a large
> probability to choose the decision of *π*I. With the increase in
> training time, *α* 1 gradually decreases until *π* completely tends to
> choose *πθ* after the accumulation of training.
>
> U(0, *δ*) denotes the neighborhood of Δf = 0. Since the randomness of
> the GP model always exists, when the output of the intelligent policy
> enters the neighborhood of the expected value, a small probability of
> jitter events will inevitably occur. When the robot system tends to
> the steady state of contact, its acceleration and force error should
> be very close to 0. At this time, the classical adaptive admittance
> control is selected as a policy to ensure accuracy.
>
> 3.6. Policy Optimization: Modified DDPG Algorithm
>
> In the actor--critic architecture of the classic DDPG, the actor is
> designed as a deter- ministic policy function At = *µθ*(St\|*θµ* ),
> where At, St are the actions of agent and state of environmental at
> time t + 1, *µθ* is the policy function, and *θµ* is the parameter of
> *µθ* . The critic is a value function Q*µ*(St, At) used to evaluate
> the policy *µθ*, and both actor and critic are fitted by a neural
> network. To correct the deviation caused by the self-iteration
>
> of a single network, DDPG sets the dual neural network model
> architecture of the target network *µ*target, Q target and online
> network *µ*online, Q online .
>
> For *πθ*, a modified DDPG algorithm is designed to optimize it, which
> is called the GP-DDPG algorithm. Specifically, *πθ* is used as the
> actor in the DDPG, and the numerical method adapted to the GP model is
> used. The following describes the adaptive changes to DDPG.
>
> As shown in Figure [4](#bookmark44), the actor in this paper is *πθ*,
> which outputs a uniquely de- termined action based only on *θ* and S.
> The critic, that is, the Q function is modeled as a neural network.
> The discrete time is t = 0, . . . , n, . . . , the state is St =
> (Xc,t, X˙c,t, Fe,t ), Xc,t ∈ Rn ×1 is the position vector of the
> end-effector in the Cartesian coordinates, X˙c,t is the velocity
> vector, and Fe,t ∈ Rn ×1 is the force/torque. At = B*θ*,t, where
> B*θ*,t ∈ Rn ×1 is a simplified form of the output damping parameter,
> in which each element is composed of diagonals in the decoupled B*θ*,t
> ∈ Rn ×n matrix form. For each state--action pair, the action value q t
> is calculated. The experience pool stores all historical data, which
> are used by the classical DDPG for experience playback, and, in this
> paper, they are also used to collect the []{#bookmark44
> .anchor}hyperparameter *θ*s .

![](./media/media/image24.jpeg){width="6.496029090113736in"
height="4.0626498250218726in"}

> **Figure 4.** The schematic diagram of the GP-DDPG algorithm.
>
> The single-dimensional Rt+1 considers error consumption cet+1 and
> energy consump- tion cdt+1 . It is expressed as follows:
>
> Rt+1 = cet+1+ cdt+1;

cet+1 = *β*ΔFT ΔF + (1 − *β*) \* E˙TE˙; *β* ∈ \[0, 1\] (17)

> ![](./media/media/image25.png){width="1.4362860892388452in"
> height="0.28436898512685915in"}
>
> where i in Equation (17) is the element of each dimension in the
> vector, *π*MAX denotes the restriction on the output, which has the
> same form as *π*t, and *ξ* is a constant coefficient.
>
> The optimization process of the objective function J is to find the
> optimal hyperparam- eter *θπ*∗ of *πθ*, so as to obtain the optimal
> solution *πθ*∗ :
>
> *πθ*∗ = argmax J(*πθ*)

![](./media/media/image26.png){width="4.774659886264217in"
height="0.4637729658792651in"}

Different from the classical DDPG, the numerical method is used in this
paper.

> As explained in Section [3.4](#bookmark43), the hyperparameters of the
> policy function in this article are *θπ* = {*θ*s, *θ*k }. The elite
> method is used for *πθ*, and the maximum likelihood estimation (MLE)
> is used to obtain *θπ*∗ .
>
> For *θ*s, different from the traditional sampling method, the prior
> data of the GP in this paper uses the elite method to select the Ne
> term with the greatest value from the experience pool H. Since *πθ*
> has the exploratory property in multiple iterations, the sampling
> method in this paper still satisfies the random sampling condition.
> The choice of *θ*s takes into account the value qn of the action An in
> each experience hn, as well as the similarity between the state Sn in
> which the experience is recorded and the current state S t.
>
> *θ*+1 = {(Sn, An )\|(Sn, An, qn ) ∈ hn ⊂ H, I(hn ) = 1}; n = 1, . . .
> , Ne ≤ t

![](./media/media/image27.png){width="4.916861329833771in"
height="0.33378390201224845in"}

> I(·) is the indicator function, which selects Ne elite samples with
> the best action value from H, \|St --- Sn \| is the Euclidean distance
> between the current state and the historical state, *ρ* 1, *ρ*2 are
> positive real numbers, and h is the historical experience in H. It
> should be noted that, to maximize the exploration rate, we update the
> qn value of the earlier data in H dynamically and randomly with the
> minibatch update of the critic network.
>
> For *θ*k, it is randomly initialized, and the optimization is
> completed by maximum likelihood estimation. In fact, the MLE is also a
> common method to deal with the hyperpa- rameter optimization of
> Gaussian distribution.

*θ*+1 = argmax log p(A\|S, *θ*); (S, A) ∈ *θ*+1 (20)

> *θ*
>
> The critic is used to estimate the qt of each At in H to select elites
> as the hyperparameter set *θ*s of the actor. In this way, the actor
> will be optimized along the direction of increasing
>
> Q. The optimization of the critic is similar to that of the classical
> DDPG using the gradient descent method:

∇*θ*Q = ESt,At,Rt+1 ∼R \[(yt --- Q(St, At; *θ*Q ))∇*θπ* Q *πθ* (St, At;
*θπθ* )\] (21)

y t = Rt + *γ*Q target(St+1, *πθ* target(St+1\|*θπ*target )\|*θ*Q target
) (22)

> Finally, the algorithm we proposed can be expressed as in Algorithm 1:

+-----------------------------------------------------------------------+
| > **Algorithm 1** GP-DDPG Algorithm Combined with Classical Control   |
+-----------------------------------------------------------------------+
| > 1: Initialize t = 0, *θ*, *θ*, *π*                                  |
| >                                                                     |
| > Initialize action value network Q*θ*                                |
| >                                                                     |
| > Initialize experience pool H                                        |
| >                                                                     |
| > 2: for episode = 1: M do                                            |
| >                                                                     |
| > 3: Receive initial observation state S0                             |
| >                                                                     |
| > 4: for t = 0: T do                                                  |
| >                                                                     |
| > 5: if *α* 1 ≥ *ε* or ΔF is in the neighborhood of zero              |
| >                                                                     |
| > 6: At = *π* (St)                                                    |
| >                                                                     |
| > 7: else                                                             |
| >                                                                     |
| > 8: At = *π* (St)                                                    |
| >                                                                     |
| > 9: end if                                                           |
| >                                                                     |
| > 10: Calculate Rt+1, observe St+1                                    |
| >                                                                     |
| > 11: Store transition h = {St, At, Rt+1, St+1, qt} in H              |
| >                                                                     |
| > 12: Get TD error with random minibatch of N transitions hn = {S, A, |
| > R+1, S+1};                                                          |
| >                                                                     |
| > n = 1, . . . , N and soft update *θ* to                             |
| > *θ*![](./media/media/image28.png){width="0.11586286089238845in"     |
| > height="0.18363735783027121in"}1                                    |
| >                                                                     |
| > 13: Calculate q′ use                                                |
| > ![](./media/media/image29.png){width="0.2189129483814523in"         |
| > height="0.14911636045494314in"}1 for each and updateq = q′ use      |
| > ![](./media/media/image30.png){width="0.2189129483814523in"         |
| > height="0.14911636045494314in"}1                                    |
| >                                                                     |
| > 14: Extract Ne samples from H based on Equation (19) as *θ*+1       |
| >                                                                     |
| > 15: Get *θ*+1 using MLE based on *θ*+1                              |
| >                                                                     |
| > 16: end for                                                         |
| >                                                                     |
| > 17: end for                                                         |
+-----------------------------------------------------------------------+

> []{#bookmark35 .anchor}**4. Results**

The algorithm is simulated in MATLAB 2022b. In the simulation, the error
of the

position controller is ignored. Without any loss of generality, only one
dimension of force

control effect is verified in this paper. The single-dimension damping
parameter output

is limited to B*π* ∈ (0,---300); *λ* in *π*I is *λ* = 1 × 10---8. For
*πθ*, there are *ξ* = 1, *β* = 0.1.

> []{#bookmark45 .anchor}4.1. Simulation Results
>
> 4.1.1. The Performance of the GP-DDPG Algorithm
>
> Firstly, the learning performance of the proposed method is verified
> and compared with the method using only GP-DDPG (without initial
> policy); the necessity of the initial policy is proven. The robot task
> environment is shown in Figure [5](#bookmark46). The base coordinate
> system is located on the robot base, and the end of effector is
> located on the X-O-Z plane, moving at a constant speed of 0.3 m/s in
> the Y direction. The environment is a plane ye = 1, perpendicular to
> the motion direction of the robot. The stiffness of the plane is Ke,
> the position of environmental is completely unknown to the robot, and
> the expected force in the Y direction is set to Fd = 20 N.
>
> For *π*I, there are KI = 0, Bfirst = 100. The Q network is designed as
> a full-connect neural network with three hidden layers; each layer has
> 64 neurons, and the initial weight is sampled from a Gaussian
> distribution.
>
> Assuming that Ke = 1000, the duration of a task is set to 15 s, and N
> = 10 learnings are performed. The obtained learning results and
> evolution process are shown in Figures
> [6](#bookmark47)--[8](#bookmark48). The addition of the classical
> algorithm provides experience for the learner. The whole algorithm
> shows a convergence trend. When N = 5, the output of *πθ* tends to be
> stable. At this time, *πθ* is still exploratory, and the fluctuation
> caused by bad trials is quickly corrected, which indicates that the
> robustness of the controller is gradually improved. After that, the
> performance of *πθ* is further improved. When the contact force
> reaches the neighborhood of the expected steady-state error, it is
> switched to the classical adaptive controller, so that ΔFss,y → 0.
> After 10 instances of learning, the model can stabilize the contact
> force at Fd = 20.

![](./media/media/image31.png){width="3.342428915135608in"
height="2.5098829833770777in"}

> []{#bookmark46 .anchor}**Figure 5.** Robot task environment. The
> environment is a vertical wall on the right side of the picture.
> []{#bookmark47 .anchor}It is assumed that there is no noise in the
> control process.

![](./media/media/image32.jpeg){width="3.937024278215223in"
height="2.4753740157480313in"}

> **Figure 6.** Trajectory correction performance of the GP-DDPG
> algorithm with initial policy.

![](./media/media/image33.jpeg){width="2.243937007874016in"
height="1.5441283902012248in"}![](./media/media/image34.jpeg){width="2.243938101487314in"
height="1.550916447944007in"}![](./media/media/image35.jpeg){width="2.2441076115485563in"
height="1.5553215223097112in"}

> (**a**) (**b**) (**c**)
>
> **Figure 7.** Force-tracking performance of the GP-DDPG algorithm with
> initial policy: (**a**) The change in contact force in 1\~10
> learnings; (**b**) N = 1\~5; (**c**) N = 5\~10.

![](./media/media/image36.jpeg){width="2.3621500437445317in"
height="1.5888265529308836in"}![](./media/media/image37.jpeg){width="2.3621500437445317in"
height="1.59163823272091in"}![](./media/media/image38.jpeg){width="2.3621489501312336in"
height="1.6000754593175852in"}

> []{#bookmark48 .anchor}(**a**) (**b**) (**c**)
>
> **Figure 8.** Damping variation performance of the GP-DDPG algorithm
> with initial policy: (**a**) The change in damping in 1\~10 learnings;
> (**b**) N = 1\~5; (**c**) N = 5\~10.
>
> The GP-DDPG algorithm without initial policy is used for simulations,
> and the other conditions remain unchanged. Figures [9](#bookmark49)and
> [10](#bookmark50)show the experimental results. It can be seen from
> the figures that, due to the lack of prior experience, the contact
> force exceeds
>
> 100 N in the first training, and the task fails. After a period of
> training, the algorithm can still converge and reach ΔFss,y → 0 at N =
> 15, but it is delayed by about 33% compared []{#bookmark49
> .anchor}with the algorithm with initial policy. It only converges to
> the locally optimal solution.

![](./media/media/image39.jpeg){width="2.3620975503062116in"
height="1.6263626421697288in"}![](./media/media/image40.jpeg){width="2.362146762904637in"
height="1.6335367454068241in"}

> (**a**) (**b**)

![](./media/media/image41.jpeg){width="2.3621489501312336in"
height="1.6760017497812774in"}

![](./media/media/image42.jpeg){width="2.3621817585301836in"
height="1.6281988188976377in"}

> (**c**) (**d**)
>
> **Figure 9.** Force-tracking performance of GP-DDPG without initial
> policy: (**a**) The change in force in 1\~15 learnings; (**b**) N =
> 1\~5; (**c**) N = 5\~10; (**d**) N = 10\~15.

![](./media/media/image43.jpeg){width="2.165277777777778in"
height="1.516415135608049in"}![](./media/media/image44.jpeg){width="2.165341207349081in"
height="1.5174245406824147in"}

> []{#bookmark50 .anchor}(**a**) (**b**)

![](./media/media/image45.jpeg){width="2.1653324584426947in"
height="1.5647101924759406in"}

![](./media/media/image46.jpeg){width="2.1652055993000876in"
height="1.5292366579177603in"}

> (**c**) (**d**)
>
> **Figure 10.** Damping variation performance of GP-DDPG without
> initial policy: (**a**) The change in damping in 1\~15 learnings;
> (**b**) N = 1\~5; (**c**) N = 5\~10; (**d**) N = 10\~15.
>
> 4.1.2. Performance of Intelligent Admittance Control in an Uncertain
> Environment
>
> In the following experiments, the rough initial position of the
> environment is known, and the speed and acceleration of the actual
> environmental change is not zero, which is the environmental
> uncertainty for which the controller has to compensate. The simulation
> results of our algorithm are compared with the constant admittance
> control and the classical adaptive admittance control shown in
> Equation (15).
>
> Firstly, the force-tracking performance under the variable stiffness
> plane is evalu- ated. The task is the same as in Section
> [4.1.1](#bookmark45), and the variable stiffness plane is ye = 0.8.
> The environmental stiffness is set to

![](./media/media/image47.png){width="3.8862106299212598in"
height="0.5213790463692038in"}

> The constant admittance control parameter is set to B = 100, K = 1 ×
> 10---6; for *π*I, there is *σ* = 0.001. Figure
> [11](#bookmark51)illustrates the trajectory-tracking and
> force-tracking performance of the three methods. It can be seen from
> the figure that all three methods can achieve force tracking. Among
> them, the intelligent admittance controller contacts the environment
> within 3--4 s, which is faster than the other two methods. When the
> environmental stiffness changes, the abrupt force causes a short
> overshoot and then recovers quickly.
>
> From Figure [11](#bookmark51), it can be seen that the evolution trend
> of the damping parameter B of classical adaptive admittance control
> and intelligent admittance control is obviously different. Overall,
> the damping parameter of intelligent admittance control is
> significantly smaller than that of classical adaptive admittance
> control, because it takes into account the energy consumption term a.
> When the robot is not in contact with the environment, the damping of
> classical adaptive admittance control increases with time, which
> reduces the acceleration of the robot and, thus, alleviates the
> unknown collision. When the contact force appears, it becomes smaller
> immediately. In the learning process, intelligent admittance control
> takes into account the speed error E˙, force error ΔF, and energy
> consumption cd. When the robot is not in contact with the environment,
> the damping parameter is reduced
>
> so that the robot can reach the environment quickly. However, small
> damping also leads to []{#bookmark51 .anchor}an increase in overshoot.

![](./media/media/image48.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}![](./media/media/image49.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}

> (**a**) (**b**)

![](./media/media/image50.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}![](./media/media/image51.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}

> (**c**) (**d**)
>
> **Figure 11.** The performance of each algorithm under the variable
> stiffness plane: (**a**) Position- tracking performance; (**b**)
> position-tracking error; (**c**) force-tracking performance; (**d**)
> evolution of the damping parameter.
>
> Next, the performance in a complex environment (the speed and
> acceleration of the environment are not constant) is evaluated. In
> this paper, a complex surface that changes according to exponential
> sine curves is designed.
>
> The contact surface changes with time as follows:

{ e==0a5· ![](./media/media/image52.png){width="6.2040682414698164e-2in"
height="0.30510608048993876in"}((0---.![](./media/media/image53.png){width="0.2842104111986002in"
height="0.34703193350831146in"}) 0.005t (24)

> The initial position of the origin of the robot end is set to y0 = 0;
> for *π*I, there is *σ* = 0.01 and Ke = 1000, and the other conditions
> remain unchanged. Compared with the sinusoidal surface, the change in
> the class-exponential sinusoidal surface is irregular, so it can
> better reflect the robustness of the algorithm. Figure
> [12](#bookmark52)illustrates the performance of the algorithm. The
> constant admittance control cannot converge. The classical adaptive
> admittance control converges after a period of adjustment. After
> training, the robustness of the intelligent admittance control is
> obviously better than the former, and the response time is faster.
>
> From Figure [12](#bookmark52), it can be seen that the evolution trend
> of the damping parameter B of classical adaptive admittance control is
> similar to that in the variable stiffness simulation. It is worth
> noting that, when the system has not yet reached the steady state, our
> algorithm obtains a smaller damping value, which makes the robot reach
> and adapt to the environ- ment faster, but also causes high-frequency
> oscillation, which may have a negative impact on the stability of the
> system in the application. In order to reduce the high-frequency
> oscillation of the system, the weight parameters in the reward can be
> adjusted appropriately to reduce the consideration of energy
> consumption cd and reduce the speed of the decrease of the damping
> parameter value.

![](./media/media/image54.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}![](./media/media/image55.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}

> []{#bookmark52 .anchor}(**a**) (**b**)

![](./media/media/image56.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}![](./media/media/image57.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}

> (**c**) (**d**)
>
> **Figure 12.** The performance of each algorithm under the complex
> environment: (**a**) Position- tracking performance; (**b**)
> position-tracking error; (**c**) force-tracking performance; (**d**)
> evolution of the damping parameter.
>
> Based on the results of the three simulations, the algorithm we
> designed has the best performance. The constant admittance control
> cannot guarantee the force tracking in all environmental stiffnesses,
> while the adaptive admittance algorithm has poor tracking effect in
> low-stiffness conditions, and there is a large jitter in the face of
> complex surfaces. Our algorithm can stably control the force error at
> the micron level, and the resistance to environmental stiffness
> changes is better than the first two.
>
> 4.2. Experimental Results
>
> To further verify the proposed algorithm, the UR10e manipulator is
> used as the ex- perimental object for experimental verification.
> Figure [13](#bookmark53)shows the environment of this experiment.
> UR10e integrates the force/torque sensor. The sensor has a Z direction
> mea- surement range of 100 N and an accuracy of 5.5 N (in the
> experiment, the read force/torque data are filtered). Using python, a
> software system based on ROS is built in the Linux environment. The
> computer communicates with the manipulator control box through the
> TCP/IP protocol, sends motion instructions, and obtains the real-time
> status of the manipulator with a ommunication frequency of 250 HZ.
>
> The experimental scene is shown in Figure [14](#bookmark54). This
> paper shows three experiments, in which the environment is a slope. In
> the first experiment, the slope of the slope was steeper, and in the
> second experiment, the slope of the slope was gentler. The third
> experiment changed the environmental characteristics and covered the
> soft rubber material at one end of the slope. Combined with the
> accuracy of the force sensor and the force drift when the end is
> suspended, the allowable force error is set to ±2.5 N in this paper.
> The experiments compare three algorithms of constant admittance,
> classical adaptive admittance, and intelligent variable admittance,
> which are the same as the algorithms compared in the simulation.
>
> The desired force of 20 N is set on the Z axis. The
> constant-admittance control parameter is set to B = 50, K = 1 × 10-6.
> Md, K*θ*, and *λ* are the same as the simulation. []{#bookmark53
> .anchor}For *π*I, there is *σ* = 0.0001 and Bfirst = 50. The
> characteristic is unknown.

![](./media/media/image58.jpeg){width="3.934507874015748in"
height="1.5331277340332459in"}

> []{#bookmark54 .anchor}**Figure 13.** Hardware configuration of the
> experiment.

![](./media/media/image59.jpeg){width="1.6140146544181977in"
height="2.0996292650918633in"}![](./media/media/image60.png){width="1.6604122922134734in"
height="2.1010498687664043in"}![](./media/media/image61.png){width="1.8541622922134733in"
height="2.1525787401574803in"}

> (**a**) (**b**) (**c**)
>
> **Figure 14.** The real-world experiment. (**a**) Experimental
> installation. The tool coordinate system is OT, the base coordinate
> system is O B, and the mapping between the two can be calculated by
> the robot kinematics. At this time, the specific location of the slope
> is unknown to the control system. (**b**) Different environments in
> the third experiment. (**c**) Experimental process. The red dotted
> line represents the trajectory of the robot.
>
> In the steep slope experiment, the end-effector is hovered above a
> hard slope, that is, the position of the base coordinate system Z =
> 0.115 m, and moves forward along the y-axis on a fixed slope.
>
> The results are shown in Figures [15](#bookmark55)--[17](#bookmark56).
> All three methods have a certain compliance effect. Under the constant
> admittance control, the end-effector of the robot collides with the
> slope after about 23 s and generates overshoot. After a period of
> adjustment, it still cannot track the required force, and the force
> error always stays at about 7.5 N.
>
> Under the classical adaptive admittance control, the end-effector of
> the robot collides with the inclined plane after about 13 s. After
> adjustment, it slowly converges to the expected force. The evolution
> trend of the damping parameter is the same as that in the simulation.
> When the environment is not contacted, the damping parameter increases
> continuously and decreases rapidly after contacting the environment.
> However, due to the measurement error in the equipment and environment
> and the influence of noise, the evolution of the damping parameter
> also has large jitter.

![](./media/media/image62.jpeg){width="2.361930227471566in"
height="1.7717191601049869in"}

![](./media/media/image63.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}![](./media/media/image64.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}

> []{#bookmark55 .anchor}(**a**) (**b**) (**c**)
>
> **Figure 15.** Constant-admittance control steep slope tracking
> performance: (**a**) force-tracking perfor- mance; (**b**)
> position-tracking performance; (**c**) evolution of the damping
> parameter.

![](./media/media/image65.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}![](./media/media/image66.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}![](./media/media/image67.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}

> (**a**) (**b**) (**c**)
>
> **Figure 16.** Classical adaptive-admittance steep control slope
> tracking performance: (**a**) force-tracking performance; (**b**)
> position-tracking performance; (**c**) evolution of the damping
> parameter.
>
> Under the control of intelligent admittance, the robot touches the
> slope and generates overshoots after about 10 s, and the desired force
> can be tracked smoothly in about 80 s. On average, the damping
> parameter of intelligent admittance control is still smaller than that
> of classical adaptive-admittance control. Compared with the latter,
> the damping change of intelligent admittance control is more obvious,
> which is also the reason why it []{#bookmark56 .anchor}reaches the
> environment faster and realizes force tracking earlier.

![](./media/media/image68.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}![](./media/media/image69.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}![](./media/media/image70.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}

> (**a**) (**b**) (**c**)
>
> **Figure 17.** Intelligent-admittance control steep slope tracking
> performance: (**a**) force-tracking perfor- mance; (**b**)
> position-tracking performance; (**c**) evolution of the damping
> parameter.
>
> The overshoot may cause damage to the robot. When other conditions
> remain un- changed, the overshoot will increase with the increase in
> the expected contact force be-
>
> cause the force error will affect the change in the motion state.
> Collision is unavoidable. In industrial assembly applications, when
> the expected force is small (which is also the purpose of force
> control), the overshoots of the three algorithms shown in the
> experiments are not enough to cause harm to the workpiece and robot in
> the assembly of high-strength metal materials, which is acceptable.
>
> In the gentle slope experiment, the position of the base coordinate
> system Z = 0.113 m, and it moves forward along the y-axis on a fixed
> slope.
>
> The results are shown in Figures [18](#bookmark57)--[20](#bookmark58).
> Under the constant-admittance control, the robot collides with the
> slope after about 16.7 s and generates overshoot. After a period of
> adjustment, it still cannot track the required force, and the force
> error always stays at about 4.8 N. Under the classical
> adaptive-admittance control, the robot collides with the inclined
> plane after about 20.5 s, and then is in a state of overshoot for some
> time. After adjustment, it converges to the expected force at 104.9 s.
> For the intelligent- admittance control, the robot touches the slope
> and generates overshoots after about 10.4 s, and the desired force can
> be tracked smoothly in about 63.9 s. It can be seen that there is no
> prominent change in the force-tracking performance of each algorithm.
>
> It is worth noting that all algorithms are slower to reach the
> environment because the initial position of the robot is farther away
> from the environment. In addition, the force over- shoot is reduced
> compared to the force overshoot of the steep slope, and the
> convergence []{#bookmark57 .anchor}speed of the variable-admittance
> algorithm is improved.

![](./media/media/image71.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}![](./media/media/image72.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}![](./media/media/image73.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}

> (**a**) (**b**) (**c**)
>
> **Figure 18.** Constant-admittance control gentle slope tracking
> performance: (**a**) force-tracking perfor- mance; (**b**)
> position-tracking performance; (**c**) evolution of the damping
> parameter.

![](./media/media/image74.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}![](./media/media/image75.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}![](./media/media/image76.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}

> (**a**) (**b**) (**c**)
>
> **Figure 19.** Classical adaptive-admittance control gentle slope
> tracking performance: (**a**) force-tracking performance; (**b**)
> position-tracking performance; (**c**) evolution of the damping
> parameter.

![](./media/media/image77.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}![](./media/media/image78.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}![](./media/media/image79.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}

> []{#bookmark58 .anchor}(**a**) (**b**) (**c**)
>
> **Figure 20.** Intelligent-admittance control gentle slope tracking
> performance: (**a**) force-tracking performance; (**b**)
> position-tracking performance; (**c**) evolution of the damping
> parameter.
>
> In the third experiment, the characteristics and location of the
> environment changed, and the setting of the slope was the same as in
> the first experiment. The position of the base coordinate system Z =
> 0.115 m, and we set the Z axis force to 5 N.
>
> The results are shown in Figures [21](#bookmark59)--[23](#bookmark60).
> Under the constant-admittance control, the robot is in contact with
> the environment at about 12.2 s, while classical adaptive- admittance
> control is about 24.2 s and intelligent-admittance control is about
> 8.6 s. When passing through the rubber material, none of the three can
> stably track the expected force, which may be caused by the overly
> soft material. However, it is gratifying that, after a small amount of
> overshoot caused by the switching of materials, all three algorithms
> can track the desired force. Among them, the constant-admittance
> control needs to experience 22.2 s, classical adaptive-admittance
> control needs to experience 18.9 s, and intelligent-admittance control
> needs to experience 12.1 s.
>
> The experimental results further prove the feasibility of the
> intelligent control algo- rithm designed in this paper. The overshoots
> of the three algorithms of constant admittance, classical adaptive
> admittance, and intelligent variable admittance are within the
> allowable range. The adjustment time of intelligent
> variable-admittance is better than that of classical
> adaptive-admittance control, and its advantages rise with the dramatic
> changes in the environment. When the experimental environment is a
> steep slope, it is improved by about 44%. When the environment is a
> gentle slope, it is improved by 39%. When there is a flexible material
> in the environment as a buffer for collision and the expected force is
> []{#bookmark59 .anchor}smaller, it is improved by 35.9%.

![](./media/media/image80.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}![](./media/media/image81.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}![](./media/media/image82.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}

> (**a**) (**b**) (**c**)
>
> **Figure 21.** Constant-admittance control for different environments'
> characteristic tracking per- formance: (**a**) force-tracking
> performance; (**b**) position-tracking performance; (**c**) evolution
> of the damping parameter.

![](./media/media/image83.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}![](./media/media/image84.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}![](./media/media/image85.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}

> (**a**) (**b**) (**c**)
>
> **Figure 22.** Classical adaptive-admittance control for different
> environments' characteristic tracking performance: (**a**)
> force-tracking performance; (**b**) position-tracking performance;
> (**c**) evolution of the []{#bookmark60 .anchor}damping parameter.

![](./media/media/image86.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}![](./media/media/image87.jpeg){width="2.3621500437445317in"
height="1.7716119860017498in"}![](./media/media/image88.jpeg){width="2.3621489501312336in"
height="1.7716119860017498in"}

> (**a**) (**b**) (**c**)
>
> **Figure 23.** Intelligent-admittance control for different
> environments' characteristic tracking per- formance: (**a**)
> force-tracking performance; (**b**) position-tracking performance;
> (**c**) evolution of the damping parameter.
>
> []{#bookmark36 .anchor}**5. Discussion and Conclusions**
>
> To improve the adaptability and safety of a robot working in an
> uncertain environment, an intelligent-admittance control algorithm
> based on DDPG combined with classical control is proposed. Simulations
> and experiments are set up to verify the performance of the algorithm.
> The algorithm is based on reinforcement learning, combines the GP
> model with DDPG, and can converge to the expected value by using fewer
> than 15 instances of learning in the simulation.
>
> To verify the superiority of the algorithm, which combines classical
> control theory and reinforcement learning theory, the control
> experiments of the algorithm without ini- tial policy and the
> algorithm with initial policy are set up. From the simulation results,
> the convergence speed of the algorithm with initial policy is 33%
> higher than that of the algorithm without initial policy. And the
> algorithm without initial policy has a task failure due to excessive
> contact force in the early stage of training. The algorithm with
> initial policy avoids this failure well, thus improving security.
>
> To verify the performance of the algorithm in an uncertain
> environment, the simu- lation of a variable-stiffness environment and
> complex environment are set up, and the results are compared with
> those of constant-admittance control and adaptive-admittance control.
> In the variable-stiffness environment experiment, all three algorithms
> can adapt to environmental changes and track the desired force. Among
> them, the speed of the intelligent-admittance algorithm to contact the
> environment is about 48% higher than that of other algorithms. In the
> complex environment experiment, assuming that the force control
> accuracy is ±0.2 N, the constant admittance control cannot track the
> contact force.
>
> The adaptive admittance control can track the contact force at about
> 26 s, while the intelli- gent admittance control can track the contact
> force at about 12 s and the speed is increased by about 53.84%.
>
> Simulation represents an ideal world. To further verify the
> performance of the pro- posed algorithm, three experiments with a
> slope as the environment were set up, and the experiment was completed
> with a 6-DOF manipulator. In the slope surface experiments, the
> constant-admittance control cannot track the desired force when the
> expected force is large. The adaptive-admittance control can track the
> contact force, but its convergence speed is not as fast as that of
> intelligent admittance control, and this gap will become more obvious
> with the change in environment. In the experiments in this paper, the
> performance of intelligent admittance control can be improved by 44%
> compared with the traditional adaptive admittance control. The
> experimental results are highly similar to the simulation results,
> which can prove the effectiveness of the simulation.
>
> The disadvantage is that, in the simulations and experiments, this
> paper only focuses on the tracking performance of the algorithm in a
> single dimension, and verifies the algorithm under a simple model. In
> the future, we plan to apply it to specific assembly tasks, consider
> more environmental interference, and conduct research on dual-robot
> collaborative assembly tasks.
>
> **Author Contributions:** Conceptualization, B.J. and X.H.;
> methodology, X.H.; software, P.R. and Y.L.; validation, B.J., X.H.,
> P.R., and Y.L.; formal analysis, X.H.; investigation, B.J.; resources,
> G.L. and B.J.; data curation, Y.L.; writing---original draft
> preparation, X.H. and L.L.; writing---review and editing, S.D., B.J.,
> X.H., and L.L.; visualization, L.L. and S.D.; supervision, G.L. and
> B.J.; funding acquisition, B.J. All authors have read and agreed to
> the published version of the manuscript.
>
> **Funding:** This research was funded by Equipment Development
> Department of People's Republic of China Central Military Commission
> grant number \[62502010223\] and Department of Science and Technology
> of Jilin Province Key R&D Project grant number \[20230201097GX\].
>
> **Institutional Review Board Statement:** Not applicable.
>
> **Informed Consent Statement:** Not applicable.
>
> **Data Availability Statement:** The original contributions presented
> in the study are included in the article; further inquiries can be
> directed to the corresponding author.
>
> **Acknowledgments:** The authors sincerely appreciate the support
> provided by their institutions.
>
> **Conflicts of Interest:** Authors Gongping Liu and Peipei Ren were
> employed by the company Avic Xi'an Aircraft Industry Group Co., Ltd.,
> author Shilin Duan was employed by the company Sichuan Huachuan
> Industry Co., Ltd.. The remaining authors declare that the research
> was conducted in the absence of any commercial or financial
> relationships that could be construed as a potential conflict of
> interest.
>
> []{#bookmark1 .anchor}**References**
>
> 1\. Kim, Y.; Yoon, W.C. Generating Task-Oriented Interactions of
> Service Robots. IEEE Trans. Syst. Man Cybern. Syst. **2014**, 44,
> 981--994. \[[CrossRef](http://doi.org/10.1109/TSMC.2014.2298214)\]
>
> 2\. Kang, T.; Yi, J.-B.; Song, D.; Yi, S.-J. High-Speed Autonomous
> Robotic Assembly Using In-Hand Manipulation and Re-Grasping.
> []{#bookmark2 .anchor}Appl. Sci. **2021**, 11, 37.
> \[[CrossRef](http://dx.doi.org/10.3390/app11010037)\]
>
> 3\. Hélio, O.; Cortesão, R. Impedance control architecture for
> robotic-assisted mold polishing based on human demonstration. IEEE
> []{#bookmark3 .anchor}Trans. Ind. Electron. **2022**, 69, 3822--3830.
> \[[CrossRef](http://dx.doi.org/10.1109/tie.2021.3073310)\]
>
> 4\. Qiao, H.; Wang, M.; Su, J.; Jia, S.; Li, R. The Concept of
> "Attractive Region in Environment" and its Application in
> High-Precision Tasks with Low-Precision Systems. IEEE ASME Trans.
> Mechatronics **2015**, 20, 2311--2327.
> \[[CrossRef](http://dx.doi.org/10.1109/TMECH.2014.2375638)\]
>
> []{#bookmark4 .anchor}5. Siciliano, B.; Villani, L. Robot Force
> Control; Springer: New York, NY, USA, 1999.
>
> 6\. Zhu, R.; Yang, Q.; Song, J.; Yang, S.; Liu, Y.; Mao, Q. Research
> and Improvement on Active Compliance Control of Hydraulic
> []{#bookmark6 .anchor}Quadruped Robot. Int. J. Control Autom. Syst.
> **2021**, 19, 1931--1943.
> \[[CrossRef](http://dx.doi.org/10.1007/s12555-020-0221-3)\]
>
> 7\. Craig, J.J.; Raibert, M.H. A systematic method of hybrid
> position/force control of a manipulator. In Proceedings of the COMPSAC
>
> 79, Proceedings, Computer Software and The IEEE Computer Society's
> Third International Applications Conference, Chicago, IL, USA, 6--8
> November 1979; pp. 446--451.
> \[[CrossRef](http://dx.doi.org/10.1109/CMPSAC.1979.762539)\]
>
> 8\. Hogan, N. Impedance Control: An Approach to Manipulation. In
> Proceedings of the 1984 American Control Conference, San []{#bookmark8
> .anchor}Diego, CA, USA, 6--8 June 1984; pp. 304--313.
> \[[CrossRef](http://dx.doi.org/10.23919/ACC.1984.4788393)\]

9\. Hu, H.; Cao, J. Adaptive variable impedance control of dual-arm
robots for slabstone installation. ISA Trans. **2022**, 128, 397--408.

> []{#bookmark9
> .anchor}\[[CrossRef](http://dx.doi.org/10.1016/j.isatra.2021.10.020)\]
>
> 10\. Liu, X.; Li, Z.; Pan, Y. Preliminary Evaluation of Composite
> Learning Tracking Control on 7-DoF Collaborative Robots. IFAC-
> []{#bookmark10 .anchor}PapersOnLine **2021**, 54, 470--475.
> \[[CrossRef](http://dx.doi.org/10.1016/j.ifacol.2021.10.399)\]
>
> 11\. Grafakos, S.; Dimeas, F.; Aspragathos, N. Variable admittance
> control in pHRI using EMG-based arm muscles co-activation. In
> Proceedings of the 2016 IEEE International Conference on Systems, Man,
> and Cybernetics (SMC), Budapest, Hungary, 9--12 October 2016; pp.
> 001900--001905.
> \[[CrossRef](http://dx.doi.org/10.1109/SMC.2016.7844516)\]
>
> 12\. Wai, H.; Hlaing, W.; Myo, A.; Sin, T. Variable admittance
> controller for physical human robot interaction. In Proceedings of the
> IECON 2017---43rd Annual Conference of the IEEE Industrial Electronics
> Society, Beijing, China, 29 October--1 November 2017; []{#bookmark41
> .anchor}pp. 2929--2934.
> \[[CrossRef](http://dx.doi.org/10.1109/IECON.2017.8216494)\]
>
> 13\. Duan, J.; Gan, Y.; Chen, M.; Dai, X. Adaptive variable impedance
> control for dynamic contact force tracking in uncertain []{#bookmark11
> .anchor}environment. Robot. Auton. Syst. **2018**, 102, 54--65.
> \[[CrossRef](http://dx.doi.org/10.1016/j.robot.2018.01.009)\]
>
> 14\. Yamane, K. Admittance Control with Unknown Location of
> Interaction. IEEE Robot. Autom. Lett. **2021**, 6, 4079--4086.
> \[[CrossRef](http://dx.doi.org/10.1109/LRA.2021.3067618)\]
>
> 15\. Abu-Dakka, F.J.; Rozo, L.; Caldwell, D.G. Force-based variable
> impedance learning for robotic manipulation. In Proceedings of the
> 2018 IEEE-RAS 18th International Conference on Humanoid Robots
> (Humanoids), Beijing, China, 6--9 November 2018; pp. 1--9.
> \[[CrossRef](http://dx.doi.org/10.1109/HUMANOIDS.2018.8624938)\]
>
> 16\. Takahashi, C.D.; Scheidt, R.A.; Reinkensmeyer, D.J. Impedance
> control and internal model formation when reaching in a randomly
> varying dynamical environment. J. Neurophysiol. **2001**, 86,
> 1047--1051.
> \[[CrossRef](http://dx.doi.org/10.1152/jn.2001.86.2.1047)\]
>
> 17\. Tsuji, T.; Tanaka, Y. Bio-mimetic impedance control of robotic
> manipulator for dynamic contact tasks. Robot. Auton. Syst. **2008**,
> 56, []{#bookmark13 .anchor}306--316.
> \[[CrossRef](http://dx.doi.org/10.1016/j.robot.2007.09.001)\]
>
> 18\. Lee, K.; Buss, M. Force Tracking Impedance Control with Variable
> Target Stiffness. IFAC Proc. Vol. **2008**, 41, 6751--6756.
> \[[CrossRef](http://dx.doi.org/10.3182/20080706-5-KR-1001.01144)\]
>
> 19\. Kronander, K.; Billard, A. Learning Compliant Manipulation
> through Kinesthetic and Tactile Human-Robot Interaction. IEEE
> []{#bookmark14 .anchor}Trans. Haptics **2014**, 7, 367--380.
> \[[CrossRef](http://dx.doi.org/10.1109/TOH.2013.54)\]
> \[[PubMed](http://www.ncbi.nlm.nih.gov/pubmed/25248219)\]
>
> 20\. Lai, G.; Liu, Z.; Zhang, Y.; Chen, C.L.P. Adaptive
> Position/Attitude Tracking Control of Aerial Robot with Unknown
> Inertial
>
> []{#bookmark15 .anchor}Matrix Based on a New Robust Neural Identifier.
> IEEE Trans. Neural Netw. Learn. Syst. **2016**, 27, 18--31.
> \[[CrossRef](http://dx.doi.org/10.1109/TNNLS.2015.2406812)\]
>
> 21\. Yang, C.; Peng, G.; Cheng, L.; Na, J.; Li, Z. Force Sensorless
> Admittance Control for Teleoperation of Uncertain Robot Manipulator
> []{#bookmark16 .anchor}Using Neural Networks. IEEE Trans. Syst. Man
> Cybern. Syst. **2021**, 51, 3282--3292.
> \[[CrossRef](http://dx.doi.org/10.1109/TSMC.2019.2920870)\]
>
> 22\. He, W.; Dong, Y.; Sun, C. Adaptive Neural Impedance Control of a
> Robotic Manipulator with Input Saturation. IEEE Trans. Syst.
> []{#bookmark17 .anchor}Man Cybern. Syst. **2016**, 46, 334--344.
> \[[CrossRef](http://dx.doi.org/10.1109/TSMC.2015.2429555)\]
>
> 23\. Liu, Y.-J.; Tong, S.; Chen, C.L.P.; Li, D.-J. Neural Controller
> Design-Based Adaptive Control for Nonlinear MIMO Systems with
> []{#bookmark18 .anchor}Unknown Hysteresis Inputs. IEEE Trans. Cybern.
> **2016**, 46, 9--19.
> \[[CrossRef](http://dx.doi.org/10.1109/TCYB.2015.2388582)\]
>
> 24\. Zhang, X.; Sun, L.; Kuang, Z.; Tomizuka, M. Learning Variable
> Impedance Control via Inverse Reinforcement Learning for
> []{#bookmark19 .anchor}Force-Related Tasks. IEEE Robot. Autom. Lett.
> **2021**, 6, 2225--2232.
> \[[CrossRef](http://dx.doi.org/10.1109/LRA.2021.3061374)\]
>
> 25\. Noppeney, V.; Boaventura, T.; Siqueira, A. Task-space impedance
> control of a parallel Delta robot using dual quaternions and a neural
> network. J. Braz. Soc. Mech. Sci. Eng. **2021**, 43, 440.
> \[[CrossRef](http://dx.doi.org/10.1007/s40430-021-03157-4)\]
>
> 26\. Hamedani, M.H.; Sadeghian, H.; Zekri, M.; Sheikholeslam, F.;
> Keshmiri, M. Intelligent Impedance Control using Wavelet Neural
> Network for dynamic contact force tracking in unknown varying
> environments. Control Eng. Pract. **2021**, 113, 104840.
> \[[CrossRef](http://dx.doi.org/10.1016/j.conengprac.2021.104840)\]
>
> 27\. Xu, Z.; Li, X.; Wu, H.; Zhou, X. Dynamic neural networks based
> adaptive optimal impedance control for redundant manipulators
> []{#bookmark20 .anchor}under physical constraints. Neurocomputing
> **2022**, 471, 149--160.
> \[[CrossRef](http://dx.doi.org/10.1016/j.neucom.2021.11.025)\]
>
> 28\. Li, G.; Yu, J.; Chen, X. Adaptive Fuzzy Neural Network Command
> Filtered Impedance Control of Constrained Robotic Manipula-
> []{#bookmark21 .anchor}tors with Disturbance Observer. IEEE Trans.
> Neural Netw. Learn. Syst. **2023**, 34, 5171--5180.
> \[[CrossRef](http://dx.doi.org/10.1109/TNNLS.2021.3113044)\]
> \[[PubMed](http://www.ncbi.nlm.nih.gov/pubmed/34587102)\]
>
> 29\. Prabhu, S.M.; Garg, D.P. Fuzzy-logic-based Reinforcement Learning
> of Admittance Control for Automated Robotic Manufacturing.
> []{#bookmark22 .anchor}Eng. Appl. Artif. Intell. **1998**, 11, 7--23.
> \[[CrossRef](http://dx.doi.org/10.1016/S0952-1976(97)00057-2)\]
>
> 30\. Roveda, L.; Maskani, J.; Franceschi, P.; Abdi, A.; Braghin, F.;
> Tosatti, J.M.; Pedrocchi, N. Model-Based Reinforcement Learning
> []{#bookmark23 .anchor}Variable Impedance Control for Human-Robot
> Collaboration. J. Intell. Robot. Syst. **2020**, 100, 417--433.
> \[[CrossRef](http://dx.doi.org/10.1007/s10846-020-01183-3)\]
>
> 31\. Li, M.; Wen, Y.; Gao, X.; Si, J.; Huang, H. Toward Expedited
> Impedance Tuning of a Robotic Prosthesis for Personalized Gait
> []{#bookmark24 .anchor}Assistance by Reinforcement Learning Control.
> IEEE Trans. Robot. **2022**, 38, 407--420.
> \[[CrossRef](http://dx.doi.org/10.1109/TRO.2021.3078317)\]
>
> 32\. Peng, G.; Chen, C.L.P.; Yang, C. Neural Networks Enhanced Optimal
> Admittance Control of Robot--Environment Interaction []{#bookmark25
> .anchor}Using Reinforcement Learning. IEEE Trans. Neural. Netw. Learn.
> Syst. **2022**, 33, 4551--4561.
> \[[CrossRef](http://dx.doi.org/10.1109/TNNLS.2021.3057958)\]
>
> 33\. Khader, S.A.; Yin, H.; Falco, P.; Kragic, D. Stability-Guaranteed
> Reinforcement Learning for Contact-Rich Manipulation. IEEE
> []{#bookmark26 .anchor}Robot. Autom. Lett. **2021**, 6, 1--8.
> \[[CrossRef](http://dx.doi.org/10.1109/LRA.2020.3028529)\]
>
> 34\. Li, C.; Zhang, Z.; Xia, G.; Xie, X.; Zhu, Q. Efficient Force
> Control Learning System for Industrial Robots Based on Variable
> []{#bookmark27 .anchor}Impedance Control. Sensors **2018**, 18, 2539.
> \[[CrossRef](http://dx.doi.org/10.3390/s18082539)\]
>
> 35\. Ding, Y.; Zhao, J.; Min, X. Impedance control and parameter
> optimization of surface polishing robot based on reinforcement
> []{#bookmark28 .anchor}learning. Proc. Inst. Mech. Eng. Part J. Eng.
> Manuf. **2023**, 237, 216--228.
> \[[CrossRef](http://dx.doi.org/10.1177/09544054221100004)\]
>
> 36\. He, W.; Mu, X.; Zhang, L.; Zou, Y. Modeling and trajectory
> tracking control for flapping-wing micro aerial vehicles. IEEE/CAA J.
> Autom. Sin. **2021**, 8, 148--156.
> \[[CrossRef](http://dx.doi.org/10.1109/JAS.2020.1003417)\]
>
> 37\. He, W.; Wang, T.; He, X.; Yang, L.-J.; Kaynak, O. Dynamical
> Modeling and Boundary Vibration Control of a Rigid-Flexible Wing
> []{#bookmark29 .anchor}System. IEEE ASME Trans. Mechatronics **2020**,
> 25, 2711--2721.
> \[[CrossRef](http://dx.doi.org/10.1109/TMECH.2020.2987963)\]
>
> 38\. Lillicrap, T.P.; Hunt,J.J.; Pritzel, A.; Heess, N.; Erez, T.;
> Tassa, Y.; Silver, D.; Wierstra, D. Continuous control with deep
> reinforcement []{#bookmark31 .anchor}learning. arXiv **2015**,
> arXiv:1509.02971.
> \[[CrossRef](http://dx.doi.org/10.48550/arXiv.1509.02971)\]
>
> 39\. Cheng, R.; Orosz, G.; Murray, R.M.; Burdick, J.W. End-to-End Safe
> Reinforcement Learning through Barrier Functions for Safety-Critical
> Continuous Control Tasks. In Proceedings of the AAAI Conference on
> Artificial Intelligence, Honolulu, HI, USA, []{#bookmark32 .anchor}27
> January--1 February 2019; pp. 3387--3395.
> \[[CrossRef](http://dx.doi.org/10.1609/aaai.v33i01.33013387)\]
>
> 40\. Berkenkamp, F.; Turchetta, M.; Schoellig, A.P.; Krause, A. Safe
> Model-based Reinforcement Learning with Stability Guarantees. In
> Advances in Neural Information Processing Systems; Curran Associates,
> Inc.: Red Hook, NY, USA, 2017 .
> \[[CrossRef](http://dx.doi.org/10.48550/arxiv.1705.08551)\]
>
> 41\. Kronander, K.; Billard, A. Stability Considerations for Variable
> Impedance Control. IEEE Trans. Robot. **2016**, 32, 1298--1305.
> []{#bookmark42
> .anchor}\[[CrossRef](http://dx.doi.org/10.1109/TRO.2016.2593492)\]
>
> **Disclaimer/Publisher's Note:** The statements, opinions and data
> contained in all publications are solely those of the individual
> author(s) and contributor(s) and not of MDPI and/or the editor(s).
> MDPI and/or the editor(s) disclaim responsibility for any injury to
> people or property resulting from any ideas, methods, instructions or
> products referred to in the content.

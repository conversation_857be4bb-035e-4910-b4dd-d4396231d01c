classdef PrioritizedReplayBuffer < handle
    % 优先经验回放缓冲区
    % 实现基于TD误差的优先级采样
    
    properties
        capacity        % 缓冲区容量
        buffer         % 经验存储
        priorities     % 优先级数组
        alpha          % 优先级指数
        beta           % 重要性采样指数
        epsilon        % 避免零优先级
        max_priority   % 最大优先级
        size           % 当前大小
        position       % 当前位置
    end
    
    methods
        function obj = PrioritizedReplayBuffer(capacity)
            % 构造函数
            
            obj.capacity = capacity;
            obj.buffer = cell(capacity, 1);
            obj.priorities = zeros(capacity, 1);
            obj.alpha = 0.6;        % 优先级指数
            obj.beta = 0.4;         % 重要性采样指数
            obj.epsilon = 1e-6;     % 避免零优先级
            obj.max_priority = 1.0; % 最大优先级
            obj.size = 0;           % 当前大小
            obj.position = 0;       % 当前位置
            
            fprintf('优先经验回放缓冲区已初始化，容量: %d\n', capacity);
        end
        
        function add(obj, state, action, reward, next_state, done)
            % 添加经验到缓冲区
            
            experience = {state, action, reward, next_state, done};
            
            % 新样本使用最大优先级
            priority = obj.max_priority;
            
            % 更新位置（循环缓冲区）
            obj.position = mod(obj.position, obj.capacity) + 1;
            obj.buffer{obj.position} = experience;
            obj.priorities(obj.position) = priority;
            
            % 更新大小
            obj.size = min(obj.size + 1, obj.capacity);
        end
        
        function [batch, indices, weights] = sample(obj, batch_size)
            % 按优先级采样经验批次
            
            if obj.size < batch_size
                % 如果经验不足，返回所有经验
                indices = 1:obj.size;
                batch = obj.buffer(indices);
                weights = ones(obj.size, 1);
                return;
            end
            
            % 计算采样概率
            priorities = obj.priorities(1:obj.size).^obj.alpha;
            probs = priorities / sum(priorities);
            
            % 按优先级采样
            indices = randsample(obj.size, batch_size, true, probs);
            
            % 计算重要性采样权重
            weights = (obj.size * probs(indices)).^(-obj.beta);
            weights = weights / max(weights);  % 归一化权重
            
            % 获取经验批次
            batch = obj.buffer(indices);
        end
        
        function update_priorities(obj, indices, td_errors)
            % 更新优先级
            
            for i = 1:length(indices)
                % 计算新优先级
                priority = (abs(td_errors(i)) + obj.epsilon)^obj.alpha;
                obj.priorities(indices(i)) = priority;
                
                % 更新最大优先级
                obj.max_priority = max(obj.max_priority, priority);
            end
        end
        
        function update_beta(obj, progress)
            % 更新beta参数（随着训练进行增加到1）
            
            obj.beta = min(1.0, obj.beta + progress * (1.0 - 0.4));
        end
        
        function is_ready = ready_for_sampling(obj, min_size)
            % 检查是否有足够的经验进行采样
            
            if nargin < 2
                min_size = 1000;  % 默认最小大小
            end
            
            is_ready = obj.size >= min_size;
        end
        
        function clear(obj)
            % 清空缓冲区
            
            obj.buffer = cell(obj.capacity, 1);
            obj.priorities = zeros(obj.capacity, 1);
            obj.size = 0;
            obj.position = 0;
            obj.max_priority = 1.0;
        end
        
        function info = get_info(obj)
            % 获取缓冲区信息
            
            info = struct();
            info.size = obj.size;
            info.capacity = obj.capacity;
            info.utilization = obj.size / obj.capacity;
            info.max_priority = obj.max_priority;
            info.avg_priority = mean(obj.priorities(1:obj.size));
            info.alpha = obj.alpha;
            info.beta = obj.beta;
        end
        
        function plot_priority_distribution(obj)
            % 绘制优先级分布
            
            if obj.size == 0
                fprintf('缓冲区为空，无法绘制优先级分布\n');
                return;
            end
            
            figure;
            histogram(obj.priorities(1:obj.size), 50);
            xlabel('优先级');
            ylabel('频次');
            title('经验优先级分布');
            grid on;
            
            % 添加统计信息
            avg_priority = mean(obj.priorities(1:obj.size));
            max_priority = max(obj.priorities(1:obj.size));
            min_priority = min(obj.priorities(1:obj.size));
            
            text(0.7, 0.8, sprintf('平均: %.3f\n最大: %.3f\n最小: %.3f', ...
                 avg_priority, max_priority, min_priority), ...
                 'Units', 'normalized', 'FontSize', 10, ...
                 'BackgroundColor', 'white', 'EdgeColor', 'black');
        end
    end
end

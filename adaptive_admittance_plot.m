%环境位置正弦变化
    %接触力图像
figure(1)
plot(out.tout,out.env_position_vary.data(:,1),'k--','LineWidth',2);
hold on
plot(out.tout,out.env_position_vary.data(:,2),'r','LineWidth',2);
hold on
plot(out.tout,out.env_position_vary.data(:,3),'b-.','LineWidth',2);
hold on
legend(["期望接触力","传统阻抗","自适应阻抗"]);
xlabel("时间");
ylabel("接触力");

  %位置图像
figure(2)
plot(out.tout,out.env_position_vary.data(:,4),'k--','LineWidth',2);
hold on
plot(out.tout,out.env_position_vary.data(:,5),'r','LineWidth',2);
hold on
plot(out.tout,out.env_position_vary.data(:,6),'b-.','LineWidth',2);
hold on
legend(["环境位置","传统阻抗","自适应阻抗"]);
xlabel("时间");
ylabel("位置");

%期望接触力突变
 %接触力图像
figure(3)
plot(out.tout,out.force_vary.data(:,1),'k--','LineWidth',2);
hold on
plot(out.tout,out.force_vary.data(:,2),'r','LineWidth',2);
hold on
plot(out.tout,out.force_vary.data(:,3),'b-.','LineWidth',2);
hold on
legend(["期望接触力","传统阻抗","自适应阻抗"]);
xlabel("时间");
ylabel("接触力");

  %位置图像
figure(4)
plot(out.tout,out.force_vary.data(:,4),'k--','LineWidth',2);
hold on
plot(out.tout,out.force_vary.data(:,5),'r','LineWidth',2);
hold on
plot(out.tout,out.force_vary.data(:,6),'b-.','LineWidth',2);
hold on
legend(["环境位置","传统阻抗","自适应阻抗"]);
xlabel("时间");
ylabel("位置");





